{"__meta": {"id": "X4a49efddfaef9066a85d341f44f78dfa", "datetime": "2025-07-12 16:35:15", "utime": **********.854493, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.420864, "end": **********.854508, "duration": 0.43364381790161133, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.420864, "relative_start": 0, "end": **********.800086, "relative_end": **********.800086, "duration": 0.37922191619873047, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.800095, "relative_start": 0.37923097610473633, "end": **********.85451, "relative_end": 2.1457672119140625e-06, "duration": 0.054414987564086914, "duration_str": "54.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45364240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027300000000000002, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.832342, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.835}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8428571, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.835, "width_percent": 19.048}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8465812, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 83.883, "width_percent": 16.117}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1280225830 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1280225830\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-692806425 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-692806425\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-522334386 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522334386\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1639563314 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338111905%7C16%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1vZ0txOEFrWk5qa0pmNHlLMXMxRVE9PSIsInZhbHVlIjoiL0F6S1JIT2lWYU5UZUIwVS9zdjgyOE91WWtPa25xWE9SZ0VyYW5CcGF4SVdNSDBOMTkyN3QzS3ZBRFBsZjBJTnI3YWhxczlVYWlHcTl5ZzJ4SDBMbnVRTFcwYlRGa1JNbGJDK3l1ak4zRitvYWhlendsVnBmblNiUytSb3VWclNmYkJqV0JVK0lubFNFenFraEJBM2QycVV4ZlZHbEl3Y2NtMUhiZjQwSk9tWklWTXZpaVBBT2ZhTkhGeWEwZlErZWdWaEJwKzIzcVNiRjF5RlZvR1BPM3hXRkFoRzR0c2JFVldyRExEM0JvUDYxampLdytZbFh1bkQ2R1h6Ull1cEphZnpwbGYvOXpSTnlXS2VDcmUxdnZ0aHhCLzhlcHoweXBGK25MRUo4Z1M0SGJWZEFvdm5rbml2VUNrY2NsZ1g2OGpOaTI3Q0FRM0pDeFVpTDJIdmNURjhvbjQxNlVKaDEwYWtwSzRjNzZyYnJiMFZtRGFlWm5RMUJjVHBhK2pyM0FqWjJJVW9hSk1ZanBJRUI4Y0U3WEpZU1hucGN4ZUNuUkhWclhsMEZBUFdCZmFJREVESjRjU2dPc2t4Q3J0RjJUTDhHalFkSklzNGlrWXlpS2xJdFFoODB5UHVaVm5UZnVjeVhVTkZUTjk5Uit0Z1NHVlVTK1ArSFpXVVRCeHEiLCJtYWMiOiIwZTM1NWUzOGY3ZGNkNTc4NDNiNTA4OWM0M2NjOTRhMjgyNzBjMzVkMDIwMmE5MzcwMjQ3MjAwNDI3NmM2OGIwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRiOXgxa3poZ1NNZ3ExRzF3ODhTbWc9PSIsInZhbHVlIjoiS25hQTQyK3dhenFWcjd3TWRPY25KZjdacDJwdk4vZ3hWT1NsMWNEUjF5cFhIb1lzMGY3UjJhS1FLbXAySGhEYmZYOXFOUWlEUGswK1BxTm9GUWtxRW5HWHhoa2hvMWc1OStxZjRLTTQ5VmN1N256RW5RSTBBL2I3TGNpeEtJTXFmYjM4ZTgrL2xhekpyT0JYc1kzYW5UTUpGOXB1OEF6QlBkVVkrcjNTYkdXekdpL1JGM1VwbkhPZzQyc2Y5QVdRbWZtOTZKaEpZck1QWkNYcGRqQWt5UVJqV09YUEk3bW40MWJXMjVLeFltSExya2RCbVBnYiswWHhJWmNGcFVFcFNOOHk5ZFZpNXp4SDRGOUV5VTg4L3dDTVNFNVMzdW9Wd29XazlRSDFna2RONTVrM3FtdEROaEphSkcxcTQwMzZPcFhaL1N4clNTYUp2YXIwYjlnQTJMa1dOYUlCTlQ5a2RwdkhieWJMVnZBMlRyUUpERXpFYWxEMC9RMUZTb2NRNitFVEFYNnVrSHl1djlmZERQQzF2cWFKWjV4U3hST0pTVnFaRUkxVC9YMEk0d2lEbGg4OTc5WHcrZ0FDQ3k3MUpEVVIra2pvY0ljNEtxaWNSWWhWR2pmeU8xUEM3NkRIUUpQV0JkbVlabVFKWkMwZ3RTYjBBaFNXRVA1VkZTd1QiLCJtYWMiOiI3NTE5ZjYzMTNkMGE1OTIyODgyNzU4M2QxMTg2NzhiZTA5Y2JjODM3ZGEwNmQ4MWE3MjViODYwOWZkOGY5OWVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639563314\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-376843129 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:35:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNDUkxqZXp3WmU0bTR1clF0bVlOU2c9PSIsInZhbHVlIjoieGxiZlM0dEdkTllvY3gxcWVacEs3ZW5TWmYvQ0RITEVWTXByc05FUHptSHZVOGlzTXNoVWtBVktvcklnamYxekhFSGdlc1BoSlJtcmJHSXJaU2pPc2JncVEyYURRcUJHYlZHS2Zmcng0dFZsN3kyMHFSNytER1BxdXNiYUxBSkJVTWNySnJlZWpRSnljR2FOd1hTejdwZ2cwczFSYWQ5c0p5bTZUanNKOFQ1VU5WVHZXSjA2Q09qZTd6MnhTaEF1cSswSEpwWkFpazVMRGlBbTJqOXFIWXduZ1dyakFtTWRPVlIrYkJwYVNjZ3NPOWhvbnFteUNFcStyb3ZsdXJkbWhjVUFzWTdHM09mQzUrd0daZWJVM21xRVl4SFVYZmJRd2ovdndMbW16NDl2TlNidUtybFZHSUJ1N2xjeFJyYmdBR0NqRFc2ZDJoSkVTMytYb1p6UmRWRmt1enZheHNlZnhkakV1OGtuaDBEb2xZN0dwdDkyVmFPZHp6VnYzY2kyMzhlUzFoVUdCNlBETEFCcXVCQTRaMDlvZVlxajhrL3JQampsVHFxYkNwYTA3WmQzKzdESm9UbWxoWVZ0TThQb1VHSlFvSG1WWXBUQzJEUjN6Y2dzQnh5UVQ2NVY2Rmxqc0JJS2NYTUNMWjRZdWlBNUwxVXEycnIzekpJN0VIZm4iLCJtYWMiOiI1NjlkOTJmNThjZDc1NzdiODBmOWEwNmY0NWMzZWU5NjEzNjgwNDkwZjc3MjEzMTQ2YjExYWY4Mjk5YWVlYjMxIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlY4cmlKeTNXbUIwNVhXMjF1dEpybHc9PSIsInZhbHVlIjoia3A4azkvaTdod3A5bEIyQ2l1K2RyaDllSzZVcWVidWVTR2UvbkNySzZsNW45YzRHejZsWEU4WkFwSWwwSzVoenVtYTRScWY1T3ZuUHUvdnkrbVNEdVBsY2NYVTk4MVZ5d0U5Z2ZSR3RkUEJQRVNaOGlVb2lpMlZqbnhZdm9IRVJlVXBjMTZLeG9XMDRVYkY5M2Y5YzNkMlN3WFhxNlJaUVA5bkczYTh1UUtvNEZOc0pwSHAyR3FjR1BicnRKbTE0UTZiWEZYV1c1L3RNQlp6QTBpeGtqMDV4eWpjblpESjh1MGNRTEUzbXhrd0w3d0FacE9HNnVSc0N4ek5FaGRaOEZ5ZVZweURZV0pjbUYyODRDSXJ0bWZSMGZxalkrdXBaa0ViZ0ZvVVFva1RjRDFYaStwZXlrdEY5Z2RZbWpSdU8ydWw4KzU5Sk9ibUdwaXhuVDM5dGdtellUeVpJQytxc2RsZjFhbXdVWlJOZjNhVjUwOVR1Zm8vVjh6TDFnSWtHUmtUb1VDNWJYcXFRWXU5VmpFenhXZGY1N1dnTjZEbjFGdEN3dy9NcUhHOFZtaGR4a3dyaFJiZndSL2JjSFlKOW1TS0VobTJpRmRxbmwvQ2oxb1oySThaVHl4RHJhemJ5YUtubk1rbVZ4ejZzUC8rMGFoWGtGbHNEeE5QOWNjb2giLCJtYWMiOiJiNDhhNWQ4MjI1ZTA2MjQ1MzEzMmM4YzdmYzc5YTgxNWU4OTRkNGRkMzc1MGRkYzI5MWJlMmEzY2Y1ZDFkNTQwIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNDUkxqZXp3WmU0bTR1clF0bVlOU2c9PSIsInZhbHVlIjoieGxiZlM0dEdkTllvY3gxcWVacEs3ZW5TWmYvQ0RITEVWTXByc05FUHptSHZVOGlzTXNoVWtBVktvcklnamYxekhFSGdlc1BoSlJtcmJHSXJaU2pPc2JncVEyYURRcUJHYlZHS2Zmcng0dFZsN3kyMHFSNytER1BxdXNiYUxBSkJVTWNySnJlZWpRSnljR2FOd1hTejdwZ2cwczFSYWQ5c0p5bTZUanNKOFQ1VU5WVHZXSjA2Q09qZTd6MnhTaEF1cSswSEpwWkFpazVMRGlBbTJqOXFIWXduZ1dyakFtTWRPVlIrYkJwYVNjZ3NPOWhvbnFteUNFcStyb3ZsdXJkbWhjVUFzWTdHM09mQzUrd0daZWJVM21xRVl4SFVYZmJRd2ovdndMbW16NDl2TlNidUtybFZHSUJ1N2xjeFJyYmdBR0NqRFc2ZDJoSkVTMytYb1p6UmRWRmt1enZheHNlZnhkakV1OGtuaDBEb2xZN0dwdDkyVmFPZHp6VnYzY2kyMzhlUzFoVUdCNlBETEFCcXVCQTRaMDlvZVlxajhrL3JQampsVHFxYkNwYTA3WmQzKzdESm9UbWxoWVZ0TThQb1VHSlFvSG1WWXBUQzJEUjN6Y2dzQnh5UVQ2NVY2Rmxqc0JJS2NYTUNMWjRZdWlBNUwxVXEycnIzekpJN0VIZm4iLCJtYWMiOiI1NjlkOTJmNThjZDc1NzdiODBmOWEwNmY0NWMzZWU5NjEzNjgwNDkwZjc3MjEzMTQ2YjExYWY4Mjk5YWVlYjMxIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlY4cmlKeTNXbUIwNVhXMjF1dEpybHc9PSIsInZhbHVlIjoia3A4azkvaTdod3A5bEIyQ2l1K2RyaDllSzZVcWVidWVTR2UvbkNySzZsNW45YzRHejZsWEU4WkFwSWwwSzVoenVtYTRScWY1T3ZuUHUvdnkrbVNEdVBsY2NYVTk4MVZ5d0U5Z2ZSR3RkUEJQRVNaOGlVb2lpMlZqbnhZdm9IRVJlVXBjMTZLeG9XMDRVYkY5M2Y5YzNkMlN3WFhxNlJaUVA5bkczYTh1UUtvNEZOc0pwSHAyR3FjR1BicnRKbTE0UTZiWEZYV1c1L3RNQlp6QTBpeGtqMDV4eWpjblpESjh1MGNRTEUzbXhrd0w3d0FacE9HNnVSc0N4ek5FaGRaOEZ5ZVZweURZV0pjbUYyODRDSXJ0bWZSMGZxalkrdXBaa0ViZ0ZvVVFva1RjRDFYaStwZXlrdEY5Z2RZbWpSdU8ydWw4KzU5Sk9ibUdwaXhuVDM5dGdtellUeVpJQytxc2RsZjFhbXdVWlJOZjNhVjUwOVR1Zm8vVjh6TDFnSWtHUmtUb1VDNWJYcXFRWXU5VmpFenhXZGY1N1dnTjZEbjFGdEN3dy9NcUhHOFZtaGR4a3dyaFJiZndSL2JjSFlKOW1TS0VobTJpRmRxbmwvQ2oxb1oySThaVHl4RHJhemJ5YUtubk1rbVZ4ejZzUC8rMGFoWGtGbHNEeE5QOWNjb2giLCJtYWMiOiJiNDhhNWQ4MjI1ZTA2MjQ1MzEzMmM4YzdmYzc5YTgxNWU4OTRkNGRkMzc1MGRkYzI5MWJlMmEzY2Y1ZDFkNTQwIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376843129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1739058187 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739058187\", {\"maxDepth\":0})</script>\n"}}