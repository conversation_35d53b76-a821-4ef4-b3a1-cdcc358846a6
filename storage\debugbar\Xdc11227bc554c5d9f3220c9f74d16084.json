{"__meta": {"id": "Xdc11227bc554c5d9f3220c9f74d16084", "datetime": "2025-07-12 16:35:35", "utime": **********.937522, "method": "GET", "uri": "/add-to-cart/2421/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.464728, "end": **********.937536, "duration": 0.4728078842163086, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.464728, "relative_start": 0, "end": **********.853885, "relative_end": **********.853885, "duration": 0.3891568183898926, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.853897, "relative_start": 0.38916897773742676, "end": **********.937537, "relative_end": 9.5367431640625e-07, "duration": 0.08363986015319824, "duration_str": "83.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48659552, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00662, "accumulated_duration_str": "6.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.888997, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 26.737}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8998039, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 26.737, "width_percent": 6.344}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9132252, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.082, "width_percent": 9.819}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.915288, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 42.9, "width_percent": 4.532}, {"sql": "select * from `product_services` where `product_services`.`id` = '2421' limit 1", "type": "query", "params": [], "bindings": ["2421"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.919879, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 47.432, "width_percent": 5.74}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2421 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2421", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.923638, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 53.172, "width_percent": 41.39}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.927808, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "kdmkjkqknb", "start_percent": 94.562, "width_percent": 5.438}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-582439830 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582439830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918955, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2422 => array:9 [\n    \"name\" => \"عصير المراعي مانجو فواكه مشكلة 300 مل\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"2422\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2421 => array:8 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"id\" => \"2421\"\n    \"originalquantity\" => 6\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2421/pos", "status_code": "<pre class=sf-dump id=sf-dump-57830162 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-57830162\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1269582483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1269582483\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1325718422 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338115835%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im80L3UyUStuRCtyUVFuWVh0SjRyOEE9PSIsInZhbHVlIjoiN01GSSs0d2sxamxyR1BENVY3QnIzdjJiK1BJQ3MzaEp0endvR3V1MDRUQWNER28vSkQ1Ykh2ZWtsdmFQR0ZHaVZvTGJJYlNPcmtwMTEzdkpMMlhCZkNyTm9TV1ZmcEowbFg3b3h4dXVEaFdFSEZTZ0dHTDFXZUJRcWc5aklGSlNPM3lxQ25WL09XaTJiM3RHWGNkNEZNWmloajB3aDE0dnJoRlJ2V0NrRHV1alBMMkliQjh2V3VKLytySWs2c2t0ejlSU08vL0ZwanoybHk0T1BmbFVMM0lHbDlQVHZCUC93VWVZLy9YWFRPTU1iT3VVblRNc2lOM3hJOTdMK0JJdG9ER3p0NWxNKzVXcUYvWWJuZUtsZlZyUXlnNXZqRXVPSFZhOVpIT2JrREIvaFN4VFBCendpL2JMSUNuVUpTWVpuekswTnlQVS82bWdZU01kWDQ2YzI3MW9xL0VyVGltNnZ5WGM1WkVCakJGN3VaQXhtRHBGeFd4K3lFMVFzUlgvM09GWU1DZjQzNWF6K1ZnSUszbVdoZHQ4Wm1XUFpza2Fkb3hrdzNyazZBMTN0SjFpWmNRc1MrUmM4aVNnb3lyaHJOUVVLYW9mK3R5ejdqOElNVzhseVZtektTUUlxbXYvY1FiMDRpVDB0YnNHUjg0MjFyZDd0cWNpTjVJOFA4UzYiLCJtYWMiOiIyNTZlZGM5YTc0YjE1MjQ2YjNmNzVmMWI2OTY4OTVmNWQwY2Q1ZjAzNDk1NmVkNzYwYTQ2MjAwODAzNGRjNjE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik93VUl3cjIwRzZDZHZVU1R0Zm1ma2c9PSIsInZhbHVlIjoiOGt1SDc2d2pqUGJmcEZUdnhPTm83S3ZxQU5PUE5vOHl1aVViNjZsWUdNa21RQmhjNGtGL1l2RllxUmIxNG0vdUNhYzhQMHpCaW1JdGFtcnB3ZUZ0UGJxTHNGVmdkUEVPMk0vZUNDWTNrMGFYN1FxUHJVRnlUc0N5ZHJEcElkdTBUbm1aMXZhK2JkbmRiRlM2Z2NIOVpibHFqTkJOTUNlb2JpRU5NYjFZMVRXNTRSdjVPU1ErTFNROUVGWG5BNG5vUnVJUXZyZWxzNjk1eXZWTHpRMWd1TTBxNDAvc2J1NmtFOUFMTVBvaE81dlkraUI2VE0zbmUvOGx5cm5IODlwM1J3cGxXUDY2NExmekl1NWlUd081S0tSL0k0dDNVODNjd0IwNHJCZ2lBMkp1SlRoMGRYYnBqaTM3UmUxYTdTVEE3NEZoRjFRdEhFVkJSMnovV2VpQTFvbGdJQ1BsbXNIR2xocEl0VXpBa0I2YVUxazBqNEVoRURQYmJYUVF1ekVVMGRYS3V4aks3UytoNm85NzRHVmJOdlFyZ1VRUXpvNVhXKzJEOWxmK1FGNk4vcWw1QTUwek1FVkx5Rjh3THlBUGN6UTZ0cmRNSjVWR3VzKzJQbVRoRW5hK3JoK3JKMjF5SmtQbzNaN3FsRkZBUFlkanBEdjNIZWtOQkRuZVBVNnAiLCJtYWMiOiJjYTFiYmZjODMzODI3MTQ5OGQ5YmMyMzllYzRlOThlYzAyMjljN2M2M2UwNTRkNmRiOTljYzA5YjU5NTlkYWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325718422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1701143407 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701143407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-967031370 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:35:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxkTW8vQVpMZE9tVkQyaHRxTERWanc9PSIsInZhbHVlIjoiSlFQV3h2U3d6SkRkNnd4TnVhczNXYm5SeWFSQURRbTBvVk9tVGpzeXZoTUJheWZVZFhPZmt4c01tQ0h3QVdxWlBQeUFtZll5TE1DeFFlY0lOUVpCZTZlWHE1TW03b1ZTWnJGR0ZlNnBuOG00TUZZSEErNnFVOVdzbjlZYjZUcVFaYzQxck5DWHQxTDE1d0NVeWJaRFZudDZ6WEdJNGxWMUFxR3pVMmNOUkJxZlplcS9hVm14UVV5WmtYZTV1Zk16VnpTRlBIanpwQlpLNGJ2WWgwM2RNTVB0eTJiM0ZzRUg4L0NKTmhRd2VsK3NabDFKUjZKbzM0NHZ6QU5CbHFBWDRsUU45Vm8zVFp1bktvWFd2Tzgwbndqc1Jzb3NzMitMdHBUS3VTL1NtRWtLeXFIZHgxYW1ydWpQUlJ1VmVibFZhZWNualludVg1ek1LaU41Q1c4V0phWldnR0NjMWlXUCs2N200ZHlzL2Mzbi9FbGI5aTZjOWVNNGdIYW9BSDBZdUMxb0FJeU1qWi81WDZYYWtGc2dubk84OGYza1hFVGMxSGtLUzhBdTJ4eVovWG5HR0Iwa2ZsM2hEdGJ4QlZjbENyZEdGM0ZVc0t5ZGR4NWtYaTdrWDRveE9hek9WNEF1emt5WmNCWkttRk5RTlhZbmV0aytEUlJIdEFGWjhCSUciLCJtYWMiOiI5MzU2MzBlMjE2N2Y0MDNlMzcyNjg4ZmYzN2IyNjBhYzE5MjRlZDlmNzU5ZTVhMTYxNGQ4N2ZhMDQ1MWFlZDU3IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImU3blI3VXl5dWlselpUbWNxR01OVVE9PSIsInZhbHVlIjoiR21FZXhiY2FGODdzOW14VW1jdkRVTmlKYUFKOHA3dFBQT29NM0ZDM0VBM21MSGNCOE1pbVBMNDROSm1IREd3V0dtNTE2Uzk0bFNVQVE5bEFIYld3YmtoMUtwT1ZZZUcvbUg3YVJTM3pzVTdVa2R1TVJxRHl3TUtrWEp1Y2Y0QWVXMlRWZXZOOVpkQUViRmxsUC90REdoaVBCOTRZYm1mUlhxb0k0SzA1VHdOR24yQWxJdGo1WnZwV1Mwc3lKVlFUVS9FTDFIQVBiRHZ6OUY3NWpjSmRseEdiRlBxbFN5NXg3OExjMDFjcmZzdGFxWW94djhSQ1hZRjVrUUJ2dG53K2hWMnd2U3JQSTRPWnh4K2NIMkREbSszVVg4TTE1OEJNUWVXTGM5cVdOTWhhYmdpNUdQNUlISjdTTTZOZXVJMkh4ME0wV3k1OVM1V1JqZDJTU0tWdlZoMW15TVBpU2FYWFJPYld0Yk9WOHRYbDYrbmRCV2RJK014RWoxZ1pYZTEvQzlIMTY1SFRGaTA1T0QzazNUOXRKZmJqKzNXZnZhejdUSFo0OHJoTThvblFRWDlQVVhHYkVFU2R1MkIzZlpGcGdKSm05eENKTWtXbDJUS2dOSmpMRFpnVEVxcUFGYzc1N1VRaHNmR3h0OHlKN0FHc29wVEgzb09lQVJnS2NIUEsiLCJtYWMiOiJkNzI1YWMwMWI4NThmNmM3MzcxMGEzMTgzMDQ0NDljNDY4MzQ3M2IxNWNiOTI3MjE3NGU1MzgyNjMwOGEwZDE0IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxkTW8vQVpMZE9tVkQyaHRxTERWanc9PSIsInZhbHVlIjoiSlFQV3h2U3d6SkRkNnd4TnVhczNXYm5SeWFSQURRbTBvVk9tVGpzeXZoTUJheWZVZFhPZmt4c01tQ0h3QVdxWlBQeUFtZll5TE1DeFFlY0lOUVpCZTZlWHE1TW03b1ZTWnJGR0ZlNnBuOG00TUZZSEErNnFVOVdzbjlZYjZUcVFaYzQxck5DWHQxTDE1d0NVeWJaRFZudDZ6WEdJNGxWMUFxR3pVMmNOUkJxZlplcS9hVm14UVV5WmtYZTV1Zk16VnpTRlBIanpwQlpLNGJ2WWgwM2RNTVB0eTJiM0ZzRUg4L0NKTmhRd2VsK3NabDFKUjZKbzM0NHZ6QU5CbHFBWDRsUU45Vm8zVFp1bktvWFd2Tzgwbndqc1Jzb3NzMitMdHBUS3VTL1NtRWtLeXFIZHgxYW1ydWpQUlJ1VmVibFZhZWNualludVg1ek1LaU41Q1c4V0phWldnR0NjMWlXUCs2N200ZHlzL2Mzbi9FbGI5aTZjOWVNNGdIYW9BSDBZdUMxb0FJeU1qWi81WDZYYWtGc2dubk84OGYza1hFVGMxSGtLUzhBdTJ4eVovWG5HR0Iwa2ZsM2hEdGJ4QlZjbENyZEdGM0ZVc0t5ZGR4NWtYaTdrWDRveE9hek9WNEF1emt5WmNCWkttRk5RTlhZbmV0aytEUlJIdEFGWjhCSUciLCJtYWMiOiI5MzU2MzBlMjE2N2Y0MDNlMzcyNjg4ZmYzN2IyNjBhYzE5MjRlZDlmNzU5ZTVhMTYxNGQ4N2ZhMDQ1MWFlZDU3IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImU3blI3VXl5dWlselpUbWNxR01OVVE9PSIsInZhbHVlIjoiR21FZXhiY2FGODdzOW14VW1jdkRVTmlKYUFKOHA3dFBQT29NM0ZDM0VBM21MSGNCOE1pbVBMNDROSm1IREd3V0dtNTE2Uzk0bFNVQVE5bEFIYld3YmtoMUtwT1ZZZUcvbUg3YVJTM3pzVTdVa2R1TVJxRHl3TUtrWEp1Y2Y0QWVXMlRWZXZOOVpkQUViRmxsUC90REdoaVBCOTRZYm1mUlhxb0k0SzA1VHdOR24yQWxJdGo1WnZwV1Mwc3lKVlFUVS9FTDFIQVBiRHZ6OUY3NWpjSmRseEdiRlBxbFN5NXg3OExjMDFjcmZzdGFxWW94djhSQ1hZRjVrUUJ2dG53K2hWMnd2U3JQSTRPWnh4K2NIMkREbSszVVg4TTE1OEJNUWVXTGM5cVdOTWhhYmdpNUdQNUlISjdTTTZOZXVJMkh4ME0wV3k1OVM1V1JqZDJTU0tWdlZoMW15TVBpU2FYWFJPYld0Yk9WOHRYbDYrbmRCV2RJK014RWoxZ1pYZTEvQzlIMTY1SFRGaTA1T0QzazNUOXRKZmJqKzNXZnZhejdUSFo0OHJoTThvblFRWDlQVVhHYkVFU2R1MkIzZlpGcGdKSm05eENKTWtXbDJUS2dOSmpMRFpnVEVxcUFGYzc1N1VRaHNmR3h0OHlKN0FHc29wVEgzb09lQVJnS2NIUEsiLCJtYWMiOiJkNzI1YWMwMWI4NThmNmM3MzcxMGEzMTgzMDQ0NDljNDY4MzQ3M2IxNWNiOTI3MjE3NGU1MzgyNjMwOGEwZDE0IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967031370\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2422</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"37 characters\">&#1593;&#1589;&#1610;&#1585; &#1575;&#1604;&#1605;&#1585;&#1575;&#1593;&#1610; &#1605;&#1575;&#1606;&#1580;&#1608; &#1601;&#1608;&#1575;&#1603;&#1607; &#1605;&#1588;&#1603;&#1604;&#1577; 300 &#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2422</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}