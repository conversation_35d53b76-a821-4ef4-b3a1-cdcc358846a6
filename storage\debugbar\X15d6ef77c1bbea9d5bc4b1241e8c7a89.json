{"__meta": {"id": "X15d6ef77c1bbea9d5bc4b1241e8c7a89", "datetime": "2025-07-12 16:35:18", "utime": **********.420367, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338117.985782, "end": **********.42038, "duration": 0.4345982074737549, "duration_str": "435ms", "measures": [{"label": "Booting", "start": 1752338117.985782, "relative_start": 0, "end": **********.335128, "relative_end": **********.335128, "duration": 0.3493461608886719, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.335137, "relative_start": 0.34935498237609863, "end": **********.420382, "relative_end": 1.9073486328125e-06, "duration": 0.08524513244628906, "duration_str": "85.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48171056, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01877, "accumulated_duration_str": "18.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3656402, "duration": 0.01275, "duration_str": "12.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.928}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3863611, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.928, "width_percent": 2.451}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.400334, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 70.378, "width_percent": 3.143}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.402237, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 73.522, "width_percent": 2.025}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.406693, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 75.546, "width_percent": 16.782}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.412117, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.328, "width_percent": 7.672}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-437294067 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437294067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.405618, "xdebug_link": null}]}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-914382442 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-914382442\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1347943103 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1347943103\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2035881299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2035881299\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1902165505 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338115835%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImljYmdJNHpJSE9Zd2VSQ0hyRGNvOWc9PSIsInZhbHVlIjoiRlJEdVNmUXlySTF3Wk1mL0xXR0xBa0drTUNJdklyNktCM1hWdmcyQk1nMis1bURzMUdKSGMvNERxWVcvZWRRT1VkbDF1cDVUWU1jeVlzTDJ2STVYTklZN0p0dFJydE15K1dNYVQ3VEcxUlI1VTJSdDI2S1Z3a1hwZStpcUNud2NsOG9Qekl5VXhEYkZORFdqV2hNRDIwdVhuMWNJcUE0dVI4Uk0xbkZJY1Y4bkNXRFVjSHE1OVRINjR2ckVEM0pwaFV5a2lkWWhsbUpVMDJxa1MrTUhxMXpqR0ZHVE5EdHBPOE81Ym1CRXdlSkIwMnNsaHRERm1PQzFiYytGY3NpT2MyNU9xZlU1SnExTDJKWHNoQ1czYy9xM0pSU0szWnNCVUNnWTdwNnVERmNmM1hvOUhxRUFCTTRzZ0VrVGdvV1NtRmVZWEFxeER4dVZhR3JZV2owQ3dxR2p1MlJWa0pMQkVXSVVsTkEvWEdPR28zRnZVcFhKdWQvMUg4M0xBdkkzTkFHWFhWakRrelpTU1huSDIwZGc0MjRlSWNYRmZhbkx3R3M5ZjZZRkdXbldVZXN1NE1xWXM5aUQ0Z2t0RHJMNzFNcGkyWnZMU0xGTFNmUVl3UnpzNVVVYTBQem92dEZaUHhLQS9oUlA4aWdRNk9JbVM1RTh4alNwLzhzQmJWNFAiLCJtYWMiOiJjZTZlZmM0OGYxNTNiNjg5OTE4ZjliNTUwMTc3ZDlhMzNmM2M5YTFjNGYyYWJkNmUwMGRlYzlkMjk4Mzc0MzFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNGd0JwYW1WdG5CdFdPTUFxaDg5OGc9PSIsInZhbHVlIjoiTk1jYVZTNGxmZGpjbGxzU201b0RMckdodE1UNW1wQ1puNzJ1am54NlhVNVpHWjVkd1pCcGJFMHlLeUF6VDR0ZUQ1Ti9BdlVyZ2NVVERhaGVoc0YyWmtDblRRalJIbDdPUVZsbHdBWFJxb3BSaUJHa05TaWZaUitQNVJ2TUlZVmttYWJCZVZISkNOK3RhRkFVMVpuVXpPT254RW54ckRiR3dobGZVdlFiYjVWMUg0UTkrL3hUZVFoOGE5dHVEZFc5TW5sdUhoKzNvNEEvMTBkNSswM1EwVy90VkRRKzN4T01SeVFWSHFkL2gxY2FPVm5WaklqU0NyL1lMRnBWbk1BMVptQVdDcmZZMnh2SzhtMFBVeTZjczZOamE2ZU84YnA4NVlyUkJROGM1UmNCWllOMk92aytBK2RaSXJuandUemk2d1NCdm16R0tUS3czQWxaUTJpUlZZaGh0L25XZjFkWEZucDBZY0JLclB5dFNpVC9PVnp0eitBeDVJeS9JaGdrZjRBM2lXQ0xGUFRHK2tMN3ZGWVZBeE8zQ1ZUalpWVW9MRHhwNU54K0FYTytoYmFoRjd5VEprYVlCRUk5OHIxWHg5RE5jYlJNZW5zNEdXNHNDT01QZWt6aVExbFRPZlljK3FaaHJSelhHSmsyN2NKOHZNTDJiQi9PYkpZdTVOQW8iLCJtYWMiOiJjNGVkYzFmMTI3MzUzYTBlYTEzM2E0ODY5NDM1NWVlYjk5Y2MyYjk3NzY3N2RhNTk3NzRmZTZkZGY4MjdhNjI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902165505\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1680707064 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680707064\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-782311484 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:35:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFiWjBlSTd0R09RUFhxa3duOXl4TXc9PSIsInZhbHVlIjoieHE5RGt3TjkvRWpRRkJJS2tKZUhBN0JiUzlXUUI4bUtLUFhjNE5pQ2Nnc3pmbitqeWNFWERpVTlEY3c0ZE1nbHJMb3JkMC94eGxSZnR2OS9jZVNDZ09oR29oa0hxd0I2Rkx0RGFDVXpGaWovRUZTQ2VjTUlEZjkySFJ3ZWlRNDhuaW5nZFRnd1RNNWNaUDMva3BjTzB4azBKK3hhMWxKVzNaaytTQWpYNzA5dko2VkhzYks3RW1jWW9QaEhHcDJyUHVIN01mZ1ZlbzRyUHFQRWo4ckpJMlEwUnJrQmxQeEthY09mR3BMd1Y4OEZTV0tuZlhHUGlsb3FkM3FFeE9zcnhjUkVmN0U1RXpmSElwbGRsSkZQU2lLZk55L1lTYzBydkFDT2JDeHhEbWRGVlczc1J6WVM3UDN1WVBkdnhMV0tiUVZpNW9uZElPcDJSVWJqR3dDM2FsNkkrOFd6Nk1tYWxFWUZzOURlMHRYSlJQS1dLR2dBV2xsMWhSekUzcmRmbWV6N1BULzFhaTU0ODdWNUdCR2ZMZ0tJaE9YK1psZnd6MXJ0ZHhnSmZ0Y0tjZVdKVkpVM1loWkMxWG5zMnBjYytmcDlDTWxNdzd2aHRZaktDckRCMENGcXdpQzA0RFgySlpKOHFhSkRnK000WmxlRVF1MXNRamNtcTF5WVdTb3giLCJtYWMiOiJkMDYzYTg1NzY5ODk3M2M3NTUzNzA0OWZmZDE2MDJiOTQ1ZDE0YzVjMmZmM2VkOGY3ZjVhZTY2NDJmNjQzOGEyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdzVk9QalgxTnNNcmhSak54Njk5WEE9PSIsInZhbHVlIjoiaFE0NUhsT0NtOWdxV2FCc1FERFluQjlwemFYVDVBV255UldRYzVDUTZNMHBMMVVRVDd0S1FmaHB1alRmUDZNTVh6M1JUd1E4T1hhL3A5akdCamNJM00xZ2c5ZlZQNjM2bHd2SzAxMXU1Q050RmJFb1RPTk1WNEVuNzhEZXM1V292Ull1WmlGdUJLS0plZ1czc1R0cm0zY0w0alROSVUydzJJdnNUZHNzc3NGNzZZaWhBMWJ1SU1UVTdUQ1ljQjZTZVVMUzZNREZEVUloQmp1RFBmNlNvWnhTa1hJVUFzU0NESW1ZT0k4KzZ0TnFUUkR4VHU0SEdMNG5HbGczTFgzbk9MN3VjbjlLZUJhK2wrZU1LSTdUbXdmL2FmL3Rjc1o3MFloNFdxQkRqSHdnTDFlRzZGRTVLdk5ZY0Zydk9RdEtEZStCOE1iSnp6TVFyeUNodjZGTVVWSGdZY2ZnZmdjcGFLR0hOOHlOWVlocGxmYXI0QmtkK2Q4WTlCbWFoU1pUYXlSSWpZNFFmVHJ1OHBPV2F6aXpoMStrckZ2ZEZzbk1RM2V5bFNOWkxUUmcyMkM2Z2MvVkdkOGxSOXgxRzcrSEZkYmh1WHI2OWVXeUpNS0x0cWk5TnNWZFdDejU0Y2lyR2VwZytpejdlVDVnMnJ6bElLVzduczVMeUUzT2d2elkiLCJtYWMiOiIyMjA2MTZjZDdiOGE1NzNlZDlhODUzNmZlZmQ0ZjI0YzdjZjkwMDc2ZWUzM2RmNjcxZGUwMmE0MGMwNWEzY2EyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFiWjBlSTd0R09RUFhxa3duOXl4TXc9PSIsInZhbHVlIjoieHE5RGt3TjkvRWpRRkJJS2tKZUhBN0JiUzlXUUI4bUtLUFhjNE5pQ2Nnc3pmbitqeWNFWERpVTlEY3c0ZE1nbHJMb3JkMC94eGxSZnR2OS9jZVNDZ09oR29oa0hxd0I2Rkx0RGFDVXpGaWovRUZTQ2VjTUlEZjkySFJ3ZWlRNDhuaW5nZFRnd1RNNWNaUDMva3BjTzB4azBKK3hhMWxKVzNaaytTQWpYNzA5dko2VkhzYks3RW1jWW9QaEhHcDJyUHVIN01mZ1ZlbzRyUHFQRWo4ckpJMlEwUnJrQmxQeEthY09mR3BMd1Y4OEZTV0tuZlhHUGlsb3FkM3FFeE9zcnhjUkVmN0U1RXpmSElwbGRsSkZQU2lLZk55L1lTYzBydkFDT2JDeHhEbWRGVlczc1J6WVM3UDN1WVBkdnhMV0tiUVZpNW9uZElPcDJSVWJqR3dDM2FsNkkrOFd6Nk1tYWxFWUZzOURlMHRYSlJQS1dLR2dBV2xsMWhSekUzcmRmbWV6N1BULzFhaTU0ODdWNUdCR2ZMZ0tJaE9YK1psZnd6MXJ0ZHhnSmZ0Y0tjZVdKVkpVM1loWkMxWG5zMnBjYytmcDlDTWxNdzd2aHRZaktDckRCMENGcXdpQzA0RFgySlpKOHFhSkRnK000WmxlRVF1MXNRamNtcTF5WVdTb3giLCJtYWMiOiJkMDYzYTg1NzY5ODk3M2M3NTUzNzA0OWZmZDE2MDJiOTQ1ZDE0YzVjMmZmM2VkOGY3ZjVhZTY2NDJmNjQzOGEyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdzVk9QalgxTnNNcmhSak54Njk5WEE9PSIsInZhbHVlIjoiaFE0NUhsT0NtOWdxV2FCc1FERFluQjlwemFYVDVBV255UldRYzVDUTZNMHBMMVVRVDd0S1FmaHB1alRmUDZNTVh6M1JUd1E4T1hhL3A5akdCamNJM00xZ2c5ZlZQNjM2bHd2SzAxMXU1Q050RmJFb1RPTk1WNEVuNzhEZXM1V292Ull1WmlGdUJLS0plZ1czc1R0cm0zY0w0alROSVUydzJJdnNUZHNzc3NGNzZZaWhBMWJ1SU1UVTdUQ1ljQjZTZVVMUzZNREZEVUloQmp1RFBmNlNvWnhTa1hJVUFzU0NESW1ZT0k4KzZ0TnFUUkR4VHU0SEdMNG5HbGczTFgzbk9MN3VjbjlLZUJhK2wrZU1LSTdUbXdmL2FmL3Rjc1o3MFloNFdxQkRqSHdnTDFlRzZGRTVLdk5ZY0Zydk9RdEtEZStCOE1iSnp6TVFyeUNodjZGTVVWSGdZY2ZnZmdjcGFLR0hOOHlOWVlocGxmYXI0QmtkK2Q4WTlCbWFoU1pUYXlSSWpZNFFmVHJ1OHBPV2F6aXpoMStrckZ2ZEZzbk1RM2V5bFNOWkxUUmcyMkM2Z2MvVkdkOGxSOXgxRzcrSEZkYmh1WHI2OWVXeUpNS0x0cWk5TnNWZFdDejU0Y2lyR2VwZytpejdlVDVnMnJ6bElLVzduczVMeUUzT2d2elkiLCJtYWMiOiIyMjA2MTZjZDdiOGE1NzNlZDlhODUzNmZlZmQ0ZjI0YzdjZjkwMDc2ZWUzM2RmNjcxZGUwMmE0MGMwNWEzY2EyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782311484\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-657039716 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657039716\", {\"maxDepth\":0})</script>\n"}}