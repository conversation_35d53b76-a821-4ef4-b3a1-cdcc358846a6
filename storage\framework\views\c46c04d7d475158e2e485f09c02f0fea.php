
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('POS Product Barcode')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('POS Product Barcode')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatable/buttons.dataTables.min.css')); ?>">
    <style>
        .product_barcode_hight_de {
            margin: 0 auto;
        }
        .barcode-box {
            border: 1px solid #000;
            padding: 10px;
            margin: 5px;
            text-align: center;
            display: inline-block;
            width: 300px;
            height: 120px;
            position: relative;
        }
        .product-name {
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
            text-align: center;
        }
        .product-price {
            font-size: 12px;
            margin-top: 3px;
            text-align: center;
        }
        .company-logo {
            position: absolute;
            top: 10px;
            right: 10px;
            max-width: 50px;
            max-height: 50px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create barcode')): ?>
            <a href="<?php echo e(route('pos.barcode.html')); ?><?php echo e(request()->getQueryString() ? '?' . request()->getQueryString() : ''); ?>"
               class="btn btn-sm btn-info me-1" data-bs-toggle="tooltip" title="معاينة HTML" target="_blank">
                <i class="ti ti-eye text-white"></i> معاينة
            </a>
            <a href="<?php echo e(route('pos.barcode.pdf')); ?><?php echo e(request()->getQueryString() ? '?' . request()->getQueryString() : ''); ?>"
               class="btn btn-sm btn-success me-1" data-bs-toggle="tooltip" title="طباعة PDF مبسط">
                <i class="ti ti-file-type-pdf text-white"></i> PDF مبسط
            </a>
            <a href="<?php echo e(route('pos.print')); ?>" class="btn btn-sm btn-primary-subtle me-1" data-bs-toggle="tooltip" title="<?php echo e(__('Print Barcode')); ?>">
                <i class="ti ti-scan text-white"></i>
            </a>
            <a data-url="<?php echo e(route('pos.setting')); ?>" data-ajax-popup="true" data-bs-toggle="tooltip" data-title="<?php echo e(__('Barcode Setting')); ?>" title="<?php echo e(__('Barcode Setting')); ?>" class="btn btn-sm btn-primary">
                <i class="ti ti-settings text-white"></i>
            </a>
        <?php endif; ?>

    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- فلتر المستودعات -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('pos.barcode')); ?>" class="row align-items-end">
                        <div class="col-md-4">
                            <label for="warehouse_id" class="form-label"><?php echo e(__('اختر المستودع')); ?></label>
                            <select name="warehouse_id" id="warehouse_id" class="form-control" onchange="this.form.submit()">
                                <option value=""><?php echo e(__('جميع المستودعات')); ?></option>
                                <?php $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($warehouse->id); ?>"
                                        <?php echo e(request('warehouse_id') == $warehouse->id ? 'selected' : ''); ?>>
                                        <?php echo e($warehouse->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label"><?php echo e(__('عرض المنتجات')); ?></label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="only_available" id="only_available"
                                    value="1" <?php echo e(request('only_available') ? 'checked' : ''); ?> onchange="this.form.submit()">
                                <label class="form-check-label" for="only_available">
                                    <?php echo e(__('المنتجات المتوفرة فقط (كمية > 0)')); ?>

                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-filter"></i> <?php echo e(__('تطبيق الفلتر')); ?>

                            </button>
                            <a href="<?php echo e(route('pos.barcode')); ?>" class="btn btn-secondary">
                                <i class="ti ti-refresh"></i> <?php echo e(__('إعادة تعيين')); ?>

                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>
                        <?php if(request('warehouse_id')): ?>
                            <?php echo e(__('منتجات مستودع: ')); ?> <?php echo e($warehouses->find(request('warehouse_id'))->name ?? ''); ?>

                        <?php else: ?>
                            <?php echo e(__('جميع المنتجات')); ?>

                        <?php endif; ?>
                        <span class="badge bg-primary ms-2"><?php echo e($productServices->count()); ?> <?php echo e(__('منتج')); ?></span>
                    </h5>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable-barcode" >
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Product')); ?></th>
                                    <th><?php echo e(__('SKU')); ?></th>
                                    <th><?php echo e(__('Price')); ?></th>
                                    <?php if(request('warehouse_id')): ?>
                                        <th><?php echo e(__('كمية المستودع')); ?></th>
                                    <?php endif; ?>
                                    <th><?php echo e(__('Barcode')); ?></th>
                                </tr>
                            </thead>

                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $productServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <?php
                                        // Calculate price with tax
                                        $price = $productService->sale_price;
                                        $taxRate = !empty($productService->tax_id) ? $productService->taxRate($productService->tax_id) : 0;
                                        $taxAmount = ($taxRate / 100) * $price;
                                        $priceWithTax = $price + $taxAmount;
                                    ?>
                                    <tr>
                                        <td><?php echo e($productService->name); ?></td>
                                        <td><?php echo e($productService->sku); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($priceWithTax)); ?></td>
                                        <?php if(request('warehouse_id')): ?>
                                            <td>
                                                <span class="badge <?php echo e(($productService->warehouse_quantity ?? 0) > 0 ? 'bg-success' : 'bg-danger'); ?>">
                                                    <?php echo e($productService->warehouse_quantity ?? 0); ?>

                                                </span>
                                            </td>
                                        <?php endif; ?>
                                        <td>
                                            <div class="barcode-box">
                                                <?php
                                                    $logo = asset(\Storage::url('uploads/logo/'));
                                                    $company_logo = \App\Models\Utility::GetLogo();
                                                ?>
                                                <img src="<?php echo e($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png')); ?>" alt="Company Logo" class="company-logo">
                                                <div id="<?php echo e($productService->id); ?>" class="product_barcode product_barcode_hight_de" data-skucode="<?php echo e($productService->sku); ?>" data-name="<?php echo e($productService->name); ?>" data-price="<?php echo e(\Auth::user()->priceFormat($priceWithTax)); ?>"></div>
                                                <div class="product-name"><?php echo e($productService->name); ?></div>
                                                <div class="product-price"><?php echo e(\Auth::user()->priceFormat($priceWithTax)); ?></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="5" class="text-center text-dark"><p><?php echo e(__('No Data Found')); ?></p></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>

    <script src="<?php echo e(asset('public/js/jquery-barcode.js')); ?>"></script>
    <script>
        $(document).ready(function() {
            $(".product_barcode").each(function() {
                var id = $(this).attr("id");
                var sku = $(this).data('skucode');
                sku = encodeURIComponent(sku);
                generateBarcode(sku, id);
            });
        });
        function generateBarcode(val, id) {

            var value = val;
            var btype = '<?php echo e($barcode['barcodeType']); ?>';
            var renderer = '<?php echo e($barcode['barcodeFormat']); ?>';
            var settings = {
                output: renderer,
                bgColor: '#FFFFFF',
                color: '#000000',
                barWidth: '1',
                barHeight: '40',
                moduleSize: '5',
                posX: '10',
                posY: '20',
                addQuietZone: '1'
            };
            $('#' + id).html("").show().barcode(value, btype, settings);

        }

        setTimeout(myGreeting, 1000);
        function myGreeting() {
            if ($(".datatable-barcode").length > 0) {
                const dataTable =  new simpleDatatables.DataTable(".datatable-barcode");
            }
        }
        // });
    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/pos/barcode.blade.php ENDPATH**/ ?>