{"__meta": {"id": "Xc4190259105041460a2ff7aa2d089558", "datetime": "2025-07-12 16:36:23", "utime": **********.172928, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338182.757299, "end": **********.172944, "duration": 0.41564512252807617, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1752338182.757299, "relative_start": 0, "end": **********.118896, "relative_end": **********.118896, "duration": 0.36159706115722656, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.118905, "relative_start": 0.3616061210632324, "end": **********.172946, "relative_end": 1.9073486328125e-06, "duration": 0.05404090881347656, "duration_str": "54.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724888, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032500000000000003, "accumulated_duration_str": "3.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.146417, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.692}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.157477, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.692, "width_percent": 16}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.163775, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.692, "width_percent": 12.308}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1389816449 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1389816449\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-187921799 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-187921799\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-727826147 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727826147\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-560349870 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338177337%7C21%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBFN0ZPc2R3SVdncm1nYnVCbWVEMFE9PSIsInZhbHVlIjoiVDRWeW1icTRpbVNxU3RhQ05EMk40c3ZZUUc0bCtqZ2lJOHZKRW5JeTBwYm1ldFNkMjR6bGdDR2dxSFlzN1JFY2hTZEFhTDc4YW9oN05DT0FSRXJEaHo3ODV5UVMxTnJ3ZkptUmZ6c3RIUDQrRFBodjFwL2RTNndES200dDlmUXZLOHJMaFIzSnlWRE9sUjdhK1cwZS80UEZLRlFaUnRtUHNZa0lLSmNTZGRMSzFtUUdUUENkd2lONFkvUzErZmxpWVVXRHo3NTBkQ0tRS242cS9PZGs2YTZ2RHBGNGk2N1hMV2p6TFRBVExUWHRWdmZRWXV4TDNyWk1xZithODZNSjJwUHFRMEZUUEF3VmlEUDNuWlpDVGh1M0RXcjhhOGpCdGU3b05tTFcyZkFjVk8wY2lPWGhScnBFemNwdHFGMHhNSzVzcFNSMGhNd251d0ladTN3Y1B3a294WTg0Uk80Umg1Ri8ybHRPUG9SQkVBQU9hdEpMdnJYaFF0b1FRZ003OUNrY1BLOVQ5Slc5ZzFDNko0YmZNSkc1a1IzajZhL09TNkFBeUNFWm05blliVEtUQmYwbDRaUmRxY0FFUDJIWlBwWjBhS1c1dXNVVENLNGxjUlR1ZGhMT29RbmlqQnQwNHdJZlFidjc0M20yQjJNMjdEYWNVVU1FRVp5NVFrL1YiLCJtYWMiOiI1MzQ1YzQ4YTM5OTE4NTNlZDg4OWZlMDg5ZTQ3NDhiOWZkYmMzMWY3OWZlOTNhNjIyNTliNDNjOGI5NGRjY2Y2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inluc2RNSWIzWVJRK1NCOWlkUlhRblE9PSIsInZhbHVlIjoiMy9uVXpTTU9ERS9ZWW9aNVpqc3Q2NnZaVXVJRjQvdG9udzNNd3ViMk9iNkRMdjJGSFN5SzBvZUdocWd0RUJXdGJjdm1lYm5aOS94QmJVNm93RWs4ZnprQUZhV1RnWVU5ck9JSWlHaXZUemY5UG1jWW9XLytNYW5YZ1JWd3JtQmlyN2YxMjd2Tlo2MkpzUVhJS3ZGRjFJTWJXNEdMeGhVMFpZUjE4bTdzZEpyMllvaS9PcGFic3dEUjlZR1ZBb250ajJ6V2hKTWdOTENwUW1yWDErQjN5dlh3WmhFTzNkQ2dMQzYxV1c4YzVPdEJLUVQrL3ZieWJMRks2bU0waG5XVnAxdzNPbjdmWXZNUTYrby9uVnFCazV4R20ydFJjaCtRT2JUdUJCTU9TWWpRazIrdGF5a0NOUnJmdW0xdlJlVW1DWTI1bE41aHRJMlVRcnVGSXY3MndWekFNWlU0TndNeS9adEhwUzZkSnBIdGV6dWV5dWJuWmplSjQ3R20rNkdzVnBhYlZJMzNXdGhqV05ncCtJLzkxV2pvM2oyM25DZUxwWVZoUXpDMW9MS1ZvNG9JTDhqcEsrOGJmc2FmcW8wekFWeFRZYk96cHNqUXQzZWxyQ1hNMmZ4WS82Qnc5R2hmaG80cTJvUkZ1d0N0VWdHUTZaaTdEQXZJY1hXMW9Tc1QiLCJtYWMiOiIxMGYxYzYzOTk4ZjBhZjQwOWNkNzliNGY5YjJlZmMwZjY1NDkwMzAzYzRiN2UwZjJjZjNkZDc5N2VkNzUwN2FjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560349870\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-481065817 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481065817\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-739432066 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVRS1lmSGdQK0FyNlh1djRqeFFXK0E9PSIsInZhbHVlIjoiY093YW85aklmTURGUmY2R3RGOWlrMVVEY3Z1dzZ6UmdsYlZNcVZLTjBLMHE2MFJPZ0syLzBsUlBEWTBaUll6cFc5RnRIK0V5b3hwTVg1RVgzZXJnclhmbytxUDhoN3U4OVZpaDRXeFlQVUxPQUxJZEhmWDFtQjRwLy9PNm5QcFFOQWxNNWFDbkhFYzFxUEcyaW0yY0F1Y1lpK2trUUpRZ2xjYjRtL2YxQU9WWEZ5NVBKekVRSTZYZ1NBWkdGZWFXWmJJWXZKTUEzV29hQ1c0QUsrWU9SR3ZaYWhtSGQzYUJ0TWtVVXEydHJ3cHIrTzVtZ2l6Q0FQRXB0VllaTkdIcWxRejFRWXRieURHcVJsM2tOR3hjM1BWWFVXb1o1cDllUjljU1hpRUJSZm1BcjRaWXR6T2xXSXdROTJPZC8zODhLbXFGenE4VUJNRW9LSHJyb05CQmRlWThaV1RXWEZhWG5mSFFob2U2TDdMTHN0MG9qZTJPbEh3eWU5WHFJNTJuTDRYZEJRRStkckRDM0FEU0lBWm0rLzYycEF6dG9MYnpCMVdOVTFDSWYzMDAwV01mWVlZeFFrdlV2OFRZdm5EN0IyTGxaRGZGaWcrdTVLZUw5WXZNT285Smd5eXI5ZzZYTm9reS9hRURsZ1dRMFNLajhrQWhKVWRDT0FiQWY0dzIiLCJtYWMiOiJlNDNlZWQzYzI1NzE4ZmE5YTQyODA5MzFjZjk0ZWI5NTY5YTQyOTdiMDczNmJjMzBlZjc0YWYxNTM0Njk4ZTUyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjM4cFZtWldZV1VYQ1dpd3JUWVp3YlE9PSIsInZhbHVlIjoiY0ZJU01Ja0VlSGUvamUzT1BRZEVSUzJTRWJXZGZBS0I0alloeXdzd2NoeERkT3ZQUkliVFlrZnh6WHZRc29kclcrMW5xQ3B0dlQxcFFPcFduYmxzSWN2UTF4bXBjTkVsYmtRaGJsbHZpN1hhaWpGSlp3S3VBWHlCeVBFL2VJc1JQT1RUZDVmcE9HeXFvcS9lMUJBWlUyeGJicnVqRnZYMG1wSG9HbjFYaDFMZCtrcUpBaWdoRWlkYytzbmEzTEU2TUR3UDJKdXlla3V3a2RtcllmVThNUG45emx5MDU4VTJkSDdPbkxvK3hKTnJ2ejUrUWZINzY5ZUcwMzAxRGlGcUNEcWJSNEVoYVdNSytDRG5QNTNoWnZOQ3V3TlE1eEQrOHFaa2VXZ21rZG1BRGd3QWhkTHZlQ0FzRncrcWE1Tjh1bk9wTVdlOXN6MFBKNkJjQTNLOHV6T0toTjRBTE03ZUVuK0I2ZkhkYm8zOHZGR05YY1VuZjZjZUdCUnFFc2FYYXZLOThFTWgySThFRDgyQmNGbG8rTTVIVEljR1U0SjU2ZkRvSTAyUDM1REdhSzdrTkxpTkoxSmpZQnFyekI1Z0FnQW5RU2d0K2lPWUFtSDF6bVJDNlNlUkhUNWxoMlFDUkYyYmNobGc5eDlGM3hXM1VTeVpxeThHbzJtREEzUG4iLCJtYWMiOiJjN2NkNDBiMmFlMzJlYjJmNzYwMzliNzc4ZTE1NzY0YTQ2MzVjNDVjZTM2MDI5MWExYjMwNTRhYjJlZjM3OGJkIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVRS1lmSGdQK0FyNlh1djRqeFFXK0E9PSIsInZhbHVlIjoiY093YW85aklmTURGUmY2R3RGOWlrMVVEY3Z1dzZ6UmdsYlZNcVZLTjBLMHE2MFJPZ0syLzBsUlBEWTBaUll6cFc5RnRIK0V5b3hwTVg1RVgzZXJnclhmbytxUDhoN3U4OVZpaDRXeFlQVUxPQUxJZEhmWDFtQjRwLy9PNm5QcFFOQWxNNWFDbkhFYzFxUEcyaW0yY0F1Y1lpK2trUUpRZ2xjYjRtL2YxQU9WWEZ5NVBKekVRSTZYZ1NBWkdGZWFXWmJJWXZKTUEzV29hQ1c0QUsrWU9SR3ZaYWhtSGQzYUJ0TWtVVXEydHJ3cHIrTzVtZ2l6Q0FQRXB0VllaTkdIcWxRejFRWXRieURHcVJsM2tOR3hjM1BWWFVXb1o1cDllUjljU1hpRUJSZm1BcjRaWXR6T2xXSXdROTJPZC8zODhLbXFGenE4VUJNRW9LSHJyb05CQmRlWThaV1RXWEZhWG5mSFFob2U2TDdMTHN0MG9qZTJPbEh3eWU5WHFJNTJuTDRYZEJRRStkckRDM0FEU0lBWm0rLzYycEF6dG9MYnpCMVdOVTFDSWYzMDAwV01mWVlZeFFrdlV2OFRZdm5EN0IyTGxaRGZGaWcrdTVLZUw5WXZNT285Smd5eXI5ZzZYTm9reS9hRURsZ1dRMFNLajhrQWhKVWRDT0FiQWY0dzIiLCJtYWMiOiJlNDNlZWQzYzI1NzE4ZmE5YTQyODA5MzFjZjk0ZWI5NTY5YTQyOTdiMDczNmJjMzBlZjc0YWYxNTM0Njk4ZTUyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjM4cFZtWldZV1VYQ1dpd3JUWVp3YlE9PSIsInZhbHVlIjoiY0ZJU01Ja0VlSGUvamUzT1BRZEVSUzJTRWJXZGZBS0I0alloeXdzd2NoeERkT3ZQUkliVFlrZnh6WHZRc29kclcrMW5xQ3B0dlQxcFFPcFduYmxzSWN2UTF4bXBjTkVsYmtRaGJsbHZpN1hhaWpGSlp3S3VBWHlCeVBFL2VJc1JQT1RUZDVmcE9HeXFvcS9lMUJBWlUyeGJicnVqRnZYMG1wSG9HbjFYaDFMZCtrcUpBaWdoRWlkYytzbmEzTEU2TUR3UDJKdXlla3V3a2RtcllmVThNUG45emx5MDU4VTJkSDdPbkxvK3hKTnJ2ejUrUWZINzY5ZUcwMzAxRGlGcUNEcWJSNEVoYVdNSytDRG5QNTNoWnZOQ3V3TlE1eEQrOHFaa2VXZ21rZG1BRGd3QWhkTHZlQ0FzRncrcWE1Tjh1bk9wTVdlOXN6MFBKNkJjQTNLOHV6T0toTjRBTE03ZUVuK0I2ZkhkYm8zOHZGR05YY1VuZjZjZUdCUnFFc2FYYXZLOThFTWgySThFRDgyQmNGbG8rTTVIVEljR1U0SjU2ZkRvSTAyUDM1REdhSzdrTkxpTkoxSmpZQnFyekI1Z0FnQW5RU2d0K2lPWUFtSDF6bVJDNlNlUkhUNWxoMlFDUkYyYmNobGc5eDlGM3hXM1VTeVpxeThHbzJtREEzUG4iLCJtYWMiOiJjN2NkNDBiMmFlMzJlYjJmNzYwMzliNzc4ZTE1NzY0YTQ2MzVjNDVjZTM2MDI5MWExYjMwNTRhYjJlZjM3OGJkIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739432066\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1554647392 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554647392\", {\"maxDepth\":0})</script>\n"}}