{"__meta": {"id": "Xa4ea5b2620ddf98f590325a6612bb70d", "datetime": "2025-07-12 16:21:51", "utime": **********.65645, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.178743, "end": **********.65647, "duration": 0.47772717475891113, "duration_str": "478ms", "measures": [{"label": "Booting", "start": **********.178743, "relative_start": 0, "end": **********.566532, "relative_end": **********.566532, "duration": 0.3877890110015869, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.56654, "relative_start": 0.38779711723327637, "end": **********.656471, "relative_end": 9.5367431640625e-07, "duration": 0.08993101119995117, "duration_str": "89.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45708040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026449999999999998, "accumulated_duration_str": "26.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5980258, "duration": 0.02536, "duration_str": "25.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.879}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.632433, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.879, "width_percent": 2.079}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.638707, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.958, "width_percent": 2.042}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1901592010 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1901592010\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-851791244 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-851791244\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-966424493 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966424493\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1541760619 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752337205003%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImIrRVRwKzIzVUhUMmh5MlJTRWk0NlE9PSIsInZhbHVlIjoiZnd2bFNYRG4wc0RBYUxxU3FVSUJhNnJ5d0FnOSswbEtlUndjMjBSd0VlUmsvam54REJZRTJ6aSsxWGZaTVdKWlAyWTZabjVTTUFqRXdPSUsvZks5R2FUNTFjbnI2ekYvOUJ2WHpmYzk2Y1BlcjdrSzMxaG9aRFMrbWNaOXI1SUNNYXJsT1g3S3R2UTlNNEFXZWwzekFXaHJwQ2QwZHhydUo1ZEROZ3NiNGdkUzAycVlOUFBYRGh2cEoyZndMTXFnQ2ZWN3VwbTBpUnFkTjBGT3cwVWRob2NobXBUVFEzbitGNWljSDZBWWk2QTV5NmYyU1VmL3pDb1JlYXpHQTlQSnFXUnBsdzdrcEw1N25DMTFnczBRK1dvT0dBYUt2UWlHSENXWTUwWFhPVDNmb1czS2FGUkVyNHZNVjM3OXIrOEEzSHdaOGU1M3R5MlU5K3FHbnhiUzNjRHdvSjlkNCtsZFJuaWFSUXNUaDREdjF4QWFpb1NielJuVVd1anVzZ3pmcS9RZmpmNU9IUTBsd0pvc24rNVpUN1R0RVN6TXEyaTd0YUlDdEZTZVIzUnBzYldTTlBzY3F1ZjNsSE44TlJPVnEwSE9xYnB3Mko5WkdjQWFaVWVDZ2FLMjBua2lKL1R3UUYxWlFjRHMvTjVkUUhBYzdJMjlDbFVRNExCeE5uOWMiLCJtYWMiOiIzMmI1Yjg3MWUzZjU3NzkyZTViMWQ2N2QxMDQwOTQ2YTM2NzkxNmNjYzMxN2QxM2EyMzcwOTNmNTMzOTZlOGRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZZZGJNckc0eFpBSXdSSmdheDI1dkE9PSIsInZhbHVlIjoidWl2RHlsMEJGT2F2STBQYlVzdzRERXBBaXdZU1h1aGlzU1JVQ1hmb1BaTWxBMEg2czVRT1lDdi9lZnJpZTBkVjlKSmdXckViQ25VM2Q5MEs2MUg4MExrby8va0dKTHQyM1BiWW9vLzhDczBhbnk4dTJ4MFAvK004dnFjOTMzc2ZHKzhFWnJkUmR4NDRxYVVNZmFEaGd1VWNRMVRpZnJFdlo3NURwV0dVcHBLeGJQbFE4NXRPWlU5NVVnQSswcW5tV3IyTk9mNjlqQmY0OVNzeERNb0pMNFU4YVdkMjA0NVFNd2RuMnhBKy9KekhZSnQ0ZkRkQWxRSXd2cVRkZE1ZazZ1N1R4Z1ZkeTlZdmtHZnl4SnFZQU9SR3E4M1gvcUFvOWVPRWFjS1A4UGxZV2FGekJmTWJZWWRXOTNTN0hTekxReEdrM2N2RmRIb09EaDVDYnJoekhMazZvZERpRmlMNVByV3FsZGQ5Vnlma1NNeHBiNnRpRFpWdkV5eWRxUkR0aUJidDhnRUhpOXNkTnhYbytrSHdSM3ZaOWVWbS90dC81a3VqRlZobklqdWpCWXFOLzBzNWx3RmlBeGllQ1VsbmNYNVFyTlkwMzlxZFR4TXdCSEUwSVIzcmlTaS9HMWhla0NnVU9sakFNeWVERFFLbDg5N0JsMVhyYmF0cWp3RGYiLCJtYWMiOiJhNjM1Mzk3ZTExZTMxMDQwOGI5ZTNjYTkxYTY3YjkzODhmZWQ1NDFiYmI0NGU3OTRkMzVmMzEyODNhODc3MzNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541760619\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1465036864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465036864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-392285416 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:21:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlROanRXYVJIVmhNcktlTDJmOTljeVE9PSIsInZhbHVlIjoiZWlITDExU24rajN0enJFVXlJQUU2UUlZeWxnUFNWVjB0cTl0akNpa3J1N2VGMWU0TFlMVnpTQUVOa1kwYWtwTkJEQnFTNUUwOHFVQ3YwRHRNMWJGQ3lxWEIwWGRXTTBEUkpFY00xTSs4RzY5dzZzd3dPMzRVUCtCQjhRZ1pMYzV3eFlxUlR3N3JnU1UrN1BVelZwRkpDQlRNMUxYa0pJbkNRSThFTU93YmZPRDhUazBkSm8wU0FJVXpCTWxpeFdjR1lDU0ZZT28xOEJxeXp3d0tRZy9yQ2JObGlRWHNsNitoVEFYSU5JdTF0WVNUMDRCcy9DWmVyYU9MNzQ2cDhza3FxcVVwMWVUUVIxZmpGNWxVbFpBbmN4Y3RzU05UZXM4czRTMWE2QzVUbVJyTXIrclFGczdwbEo5RFVnRjJCR09QL1ByVjN1cHk3VzVvcDVSVmJBS0RQR3J3dy9FU0pWajBPdnJFY2hqelkxMzYyaWw0a3ZZZGxRcGJLNEwwd0FTWk1Sc3pKWWhOZTlCU01mcW5CMmpVZ2gyc0ZrTnQ5bGU0eDNFMUwvUTI0RnRkZ0V0UVVHQWd2UDJVbU5GcGVoVVpRQjVidUpJN0p1dVFBd3h0MTUwNzE3UzVHQU9QOHFHQmZ4U3lXVjFISjRlekVhU2tRNzZUcXFURExZV05BbmsiLCJtYWMiOiI2YzQ2ZGQ4MjhhOTY0NGY1YTA1OGE0YjQ2NDMyMjZiYmY4MDlmNzFjOTEzOTExYmZjMTZjZGNmOGQ1NmZjNTAxIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:21:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRIY0xSU0ZsbkVuNmhHK21wVnBtQ0E9PSIsInZhbHVlIjoiOWRtcE41dDBaYUpwMFpXU2cycFhSbGtRb1ZjKy93Y3lEZUlGSWVsMXdYbHlJajZBUXFLRnd3VVRNbm1oaS9xYkx2TlRPUTJEcE5aSWVycmxKbDJIVkRZejZ3TjF6dTBCbWxzQzI0aW9tK2tFdldNU0NxbWorL3lsRlp3ZHJTaTROQlN0dy9XT1VpQjBlOUw1eUtWMzloOVRiTEVYdExZeGVpVDdHaXdIc1BZaVJNVkg4MEg5QXJ5c05zbDdZTmNlWk1reHA2SzdvVEdFMUE3RjlrQTFwNzBsc003ZWdyU1FsREVZNUt6eFM1d0VsaWhnRGpERFdWeHlFWGJNell6OXM4WCtTWm1SbWlPWEtmWkFYSVFDeWFUVzRlZ2o2NjF0bk40aWRWYUsyNGxqZ3pKTnk4emVLUVJyZDQrcExESXRiNitBVlFJaVBpaWZIUzJHMzhGRXlOYy8yK096VEN1dWZFd1l0UWtuTTN4bGRMRmNZWHFXUGNNVkFIOGdMZXhjU2RjeFF4Qm1JcmpLSWVjVEp5UjZVZkJ4TXFDMEE1M1c4NkxHaXh3OXVscVoyM0FrWFFNZElUMXo3c2tCMi9BNXdyOThaaE5EMlF6N2dLS2RKaHQ5bXhNdHJsRW1rbnVnRGFXME5VYzhLN0xwcjVkRTVhdDVOY21sRnhTMCtxc1QiLCJtYWMiOiIwMTdjYTBiYzU4OTVkOTVmYThiYTViZDVlMTQwYjFmY2VhOTgzYzcyZTEzZDgyNjgzMjAwMmExOTYwMmMxMDBlIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:21:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlROanRXYVJIVmhNcktlTDJmOTljeVE9PSIsInZhbHVlIjoiZWlITDExU24rajN0enJFVXlJQUU2UUlZeWxnUFNWVjB0cTl0akNpa3J1N2VGMWU0TFlMVnpTQUVOa1kwYWtwTkJEQnFTNUUwOHFVQ3YwRHRNMWJGQ3lxWEIwWGRXTTBEUkpFY00xTSs4RzY5dzZzd3dPMzRVUCtCQjhRZ1pMYzV3eFlxUlR3N3JnU1UrN1BVelZwRkpDQlRNMUxYa0pJbkNRSThFTU93YmZPRDhUazBkSm8wU0FJVXpCTWxpeFdjR1lDU0ZZT28xOEJxeXp3d0tRZy9yQ2JObGlRWHNsNitoVEFYSU5JdTF0WVNUMDRCcy9DWmVyYU9MNzQ2cDhza3FxcVVwMWVUUVIxZmpGNWxVbFpBbmN4Y3RzU05UZXM4czRTMWE2QzVUbVJyTXIrclFGczdwbEo5RFVnRjJCR09QL1ByVjN1cHk3VzVvcDVSVmJBS0RQR3J3dy9FU0pWajBPdnJFY2hqelkxMzYyaWw0a3ZZZGxRcGJLNEwwd0FTWk1Sc3pKWWhOZTlCU01mcW5CMmpVZ2gyc0ZrTnQ5bGU0eDNFMUwvUTI0RnRkZ0V0UVVHQWd2UDJVbU5GcGVoVVpRQjVidUpJN0p1dVFBd3h0MTUwNzE3UzVHQU9QOHFHQmZ4U3lXVjFISjRlekVhU2tRNzZUcXFURExZV05BbmsiLCJtYWMiOiI2YzQ2ZGQ4MjhhOTY0NGY1YTA1OGE0YjQ2NDMyMjZiYmY4MDlmNzFjOTEzOTExYmZjMTZjZGNmOGQ1NmZjNTAxIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:21:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRIY0xSU0ZsbkVuNmhHK21wVnBtQ0E9PSIsInZhbHVlIjoiOWRtcE41dDBaYUpwMFpXU2cycFhSbGtRb1ZjKy93Y3lEZUlGSWVsMXdYbHlJajZBUXFLRnd3VVRNbm1oaS9xYkx2TlRPUTJEcE5aSWVycmxKbDJIVkRZejZ3TjF6dTBCbWxzQzI0aW9tK2tFdldNU0NxbWorL3lsRlp3ZHJTaTROQlN0dy9XT1VpQjBlOUw1eUtWMzloOVRiTEVYdExZeGVpVDdHaXdIc1BZaVJNVkg4MEg5QXJ5c05zbDdZTmNlWk1reHA2SzdvVEdFMUE3RjlrQTFwNzBsc003ZWdyU1FsREVZNUt6eFM1d0VsaWhnRGpERFdWeHlFWGJNell6OXM4WCtTWm1SbWlPWEtmWkFYSVFDeWFUVzRlZ2o2NjF0bk40aWRWYUsyNGxqZ3pKTnk4emVLUVJyZDQrcExESXRiNitBVlFJaVBpaWZIUzJHMzhGRXlOYy8yK096VEN1dWZFd1l0UWtuTTN4bGRMRmNZWHFXUGNNVkFIOGdMZXhjU2RjeFF4Qm1JcmpLSWVjVEp5UjZVZkJ4TXFDMEE1M1c4NkxHaXh3OXVscVoyM0FrWFFNZElUMXo3c2tCMi9BNXdyOThaaE5EMlF6N2dLS2RKaHQ5bXhNdHJsRW1rbnVnRGFXME5VYzhLN0xwcjVkRTVhdDVOY21sRnhTMCtxc1QiLCJtYWMiOiIwMTdjYTBiYzU4OTVkOTVmYThiYTViZDVlMTQwYjFmY2VhOTgzYzcyZTEzZDgyNjgzMjAwMmExOTYwMmMxMDBlIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:21:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392285416\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1836610059 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836610059\", {\"maxDepth\":0})</script>\n"}}