{"__meta": {"id": "X7587bf1033287351ba3cffa6e8fd8e8b", "datetime": "2025-07-12 16:36:04", "utime": **********.381978, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338163.956391, "end": **********.381994, "duration": 0.42560291290283203, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1752338163.956391, "relative_start": 0, "end": **********.323918, "relative_end": **********.323918, "duration": 0.3675270080566406, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.323927, "relative_start": 0.3675358295440674, "end": **********.381995, "relative_end": 9.5367431640625e-07, "duration": 0.058068037033081055, "duration_str": "58.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722744, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031200000000000004, "accumulated_duration_str": "3.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.355288, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.603}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.366386, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.603, "width_percent": 11.859}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.372622, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.462, "width_percent": 11.538}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1109285351 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1109285351\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-740153218 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-740153218\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-4396910 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4396910\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338161110%7C18%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im0xaDM1OG1vdDRNNmFMSTl5MjEwNXc9PSIsInZhbHVlIjoiZ2FZL3ZWckZvUVppbkZtYVZUanZlUlA3OEFJV0gwQko3cjdRYmlnblY3eEJIdk03Qy9acTRkdk1nZksvMlRmVlZ5aHNpU2JnMTAyYTlMc3cyd3FlNUZIVVdMbVliVmptY3l5MG1DL2NKSXRNNmRFR2pQOTg0TWhybmxvc24zYnlVZFVwSi9YZjRYOFRtRVFJZ2RVQjQxTzRMczBEY1N2UGxSV2N2YSszUDl0NERRYnhvNnBmZEZ0NXhEREpQRlpNRDY2a0w2TE1RWUdnTXZ1cGg2Q0ppY3lpMlJ6a1FCUWk4ZGhCOW13bFNiZldCYlUyd3YzbklTYWpCS0pBa0VSRy9TeThpbE1MSWNQMkErNFIyQlJ4by8zTDlwZk5pS0Y2UTR3U1pydHkwS2pLRGFoM0laOElOZUJyOWhuQ1BCZkF4cGFlbjM3VWpLeUdZQVRmMjJZaUVHWVVFYVJKV2hFdXZYd2p2TlFteXlLMnd5UWVpMVU5UmxyazYzZytrcnpKNENFSVFjN1MyMndvZ0lDYVRrcHVUZWlUR3FIbHd6M01UNUM4anlmQkdtakJiNnk1WGJrVHVQK28ybnd5WUpGbldRTEJkTWVMRGZpR0tyM204K3NhRFkyRnpiUi8vTkNtampUZXR4LzlVVGV2dVFYajFla3JNT1UvdXh4Y2lGbVkiLCJtYWMiOiI0M2Q1ZGZiMzU3NTYzZjhmYWMyNGRhZWYzZGZlM2I3NjM3MDUwMDU1ZjFlMzdlZWM1NWQ3N2YwZTNlMzJjYzFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5jb05GbVk1K2p0RVJ5YnBTWFM3R3c9PSIsInZhbHVlIjoienNTaEtOQnNPWWNXVFR3MUlnaW1ucWhUNnhTZE1RSFpUWlhxUDJ0L2sxU3oxWWdhbzlTTmJDNjNsb1JoMmtENmFWMCtRWjhGODYweGxRSmpJdGpMSnpDTVA3SXMzUXVNcktDaURuSVRZUzdhK3pHZEJDTmF2V0FQa1N6anJNTUo1S0FDckRwVGROWW5obHlXTWcxc3B1em9rMlYrK3BYN01tajZvc21Hbyt5d0kvZXJYMU5DWFVHMWcyaDFDa21UbGcrS1pnblk1dlRDenlJek9OaFBrbDhXUVIyQ0NmUVBVcmFmUHhvWTd4T2tuLy9Sa3haK1VaOEFYd3pmcy80c1dEM3NPa1ZEV1F6cTVjWGlMMmcrT2Zma1VneWVTRUV5L205dzlUSlMvK1lXMlRjeVRiOHZBeXkyeXZpcjUrRHF2alZjUVkxZFV3ZmlnMjFhQWlKRGdrRmRkMFlCcTljUzZHNmFwUXh4Y2gyakJnTXVqZHV5M2p6eEN2RFl2SW9FaE01aTZpZHlzWWtXcnpac1RwVExFQktrSCtoZHpsT0dVcXJpc2tqR0ErWHVjdWlDdzNBTmxUQ25Qa0NvWVhuYnBvQmd2VXdkUnFraTlvV3dLOUdrYzJoTTJ1eHZGMTlJcnBJb25BbHVGTmhzVDhkamZmWldEeCtLR3VaUnNvRkUiLCJtYWMiOiI0YzMyNjUyMzc0NGM1MzA3ZmE4MzY4OTFmN2YyNjBlNTg5NzM4YzYzNjVhMzcwMTJhYzg3ZjdkM2EyZDZiNTEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlllMG1RRUhVUGo1Mk5yTEV2Q0VvY0E9PSIsInZhbHVlIjoiVjRnWnk5WW9CTXE4UVFJY2ZEUUtodlkzVXZVYWM4TkxDV283dmdMUlZYcmNjcm80U3R5RUJEYW55UjBQY0tSQ1NGdk1OcEFmQk1vTElJWHVCRlVlekZRdTBvNlpqdUF4Q2ZZeEt2ZlJ3VDZNczdwTFRNbXFuc01LWjljaEpjK2ZkeWlETkREaUpVUHR6Ky9idkZqNVk3TklVeUxiZWVoeGJKck1pSEdzTm56ei9lN21iRm5oUnlvNE5hVFRLdktqbDNJRzZKNzRvUGh1REhzbDVkOWZOUFNuKzRHYy8vSm1MZnVmMnl4b0FkK2VwSi9JbGsxRkk2aGhLd1FjekFKOHBhcWp1ZGp2N2NXellscXJlajBPNzdTbCt1RzFwMllBL3MzNEQzTUFZTXRIZGxDaXJpZk1qbHh3RkljU0JqM2xGYkNlN1RIZW5GeE9RWkEvRk1IdU95S1lnMXdxakhCeE9RTFJsYnJGUkV2TDZRZlovU3p6SldVamZLMlNCQjlXMkpNejhpR09nRnpGb1R3Mmh1dFJpeGk2QTgzK1BsNnp0dXUvQkJxMnBueVNiajJ4VHdiREFXekVoengvK3R3a3NLSVJqZDhCbUM5TlNuQ2Q2bFVpcURUeEV1c29oMnlBWUcwUXFwbGdBbDhXcG4wcUN2aU4ydEFNZjQxaTlRWi8iLCJtYWMiOiJjMDU3MWFmYzM0NjhiNDhmZGY3MzAxMjZmN2NjODQ1M2Y4NjNmMTYyOTYxMzliNzY4YzFmNjk4ZDVmNTU0ZDNmIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJmMG11Y29yZGFKVm5vaXRNVUxCNnc9PSIsInZhbHVlIjoiakw3T2tMR1loQkhVaWdDenhMYUtDM2VtNklBa0dMVmdvK3JMdElaUjY2MmRXYmdBbXdPSCtCTVVUa1ZwQ0tBYVJLdDVFVlZvS08rOWNvU3d0aFU5dVBLL3oxSWRXM3gzNTVyb1A4ZWhmdnh5SUh2S0szVG1GODFnU1lzYS85RW0xUjB4QmpObjlCYkVBaGNnVGpmaUxESkkvaTlhRzZQRDVNbjRvWlRJbnlwZjh4dForOGNnYUlZTnlKL3Jpd0dWaEpLcUlNSTlEUXIyK0E2SU1ZZkNxc0tER1l6eUpJQ3BDK3hmbnlFeVZvSmZNZFJUdFJmSWxWS09vNnJmSEk1cEJQQUIybFJNZiswbEZiUnZPR1VpTUdFOXNKTDRaVWZWSWQzUjkvUE9TSEUySjl3QWQwZEdvVm5LT2xyd0pOVDE5K2NjV2N1K1FPbUFjalNpSjZlWGtMMDFOYUE5NXJQa2JXNGxYRGRYYjRKM0Z4YTI5ZUpiOVQrQkdxcXNPZjY5NDhLd215Q1dieXJsZVkyTEJReGJodTFMZ2lqVXdVd1RTd2FpdEF1NFVFSEtldFdTcjBoQVF1U1YvZ29CUTN0WG10Zk5hSmZZcVZRc3g4Y3BmczlQTWVjMFNKQVF2ZytqT3F2VG1DN0VSZkRwTG9GTVFPMERrbFZXTXhFa0xEbnQiLCJtYWMiOiI0Y2M2OTg4ZmIzMTIyMzEzNTk0MDAxYjcwMTI5MGEyNTcxNDk3YzU4OWFhMzRjMTY4ODg1YTQ0Y2ZkYzgyZmRlIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlllMG1RRUhVUGo1Mk5yTEV2Q0VvY0E9PSIsInZhbHVlIjoiVjRnWnk5WW9CTXE4UVFJY2ZEUUtodlkzVXZVYWM4TkxDV283dmdMUlZYcmNjcm80U3R5RUJEYW55UjBQY0tSQ1NGdk1OcEFmQk1vTElJWHVCRlVlekZRdTBvNlpqdUF4Q2ZZeEt2ZlJ3VDZNczdwTFRNbXFuc01LWjljaEpjK2ZkeWlETkREaUpVUHR6Ky9idkZqNVk3TklVeUxiZWVoeGJKck1pSEdzTm56ei9lN21iRm5oUnlvNE5hVFRLdktqbDNJRzZKNzRvUGh1REhzbDVkOWZOUFNuKzRHYy8vSm1MZnVmMnl4b0FkK2VwSi9JbGsxRkk2aGhLd1FjekFKOHBhcWp1ZGp2N2NXellscXJlajBPNzdTbCt1RzFwMllBL3MzNEQzTUFZTXRIZGxDaXJpZk1qbHh3RkljU0JqM2xGYkNlN1RIZW5GeE9RWkEvRk1IdU95S1lnMXdxakhCeE9RTFJsYnJGUkV2TDZRZlovU3p6SldVamZLMlNCQjlXMkpNejhpR09nRnpGb1R3Mmh1dFJpeGk2QTgzK1BsNnp0dXUvQkJxMnBueVNiajJ4VHdiREFXekVoengvK3R3a3NLSVJqZDhCbUM5TlNuQ2Q2bFVpcURUeEV1c29oMnlBWUcwUXFwbGdBbDhXcG4wcUN2aU4ydEFNZjQxaTlRWi8iLCJtYWMiOiJjMDU3MWFmYzM0NjhiNDhmZGY3MzAxMjZmN2NjODQ1M2Y4NjNmMTYyOTYxMzliNzY4YzFmNjk4ZDVmNTU0ZDNmIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJmMG11Y29yZGFKVm5vaXRNVUxCNnc9PSIsInZhbHVlIjoiakw3T2tMR1loQkhVaWdDenhMYUtDM2VtNklBa0dMVmdvK3JMdElaUjY2MmRXYmdBbXdPSCtCTVVUa1ZwQ0tBYVJLdDVFVlZvS08rOWNvU3d0aFU5dVBLL3oxSWRXM3gzNTVyb1A4ZWhmdnh5SUh2S0szVG1GODFnU1lzYS85RW0xUjB4QmpObjlCYkVBaGNnVGpmaUxESkkvaTlhRzZQRDVNbjRvWlRJbnlwZjh4dForOGNnYUlZTnlKL3Jpd0dWaEpLcUlNSTlEUXIyK0E2SU1ZZkNxc0tER1l6eUpJQ3BDK3hmbnlFeVZvSmZNZFJUdFJmSWxWS09vNnJmSEk1cEJQQUIybFJNZiswbEZiUnZPR1VpTUdFOXNKTDRaVWZWSWQzUjkvUE9TSEUySjl3QWQwZEdvVm5LT2xyd0pOVDE5K2NjV2N1K1FPbUFjalNpSjZlWGtMMDFOYUE5NXJQa2JXNGxYRGRYYjRKM0Z4YTI5ZUpiOVQrQkdxcXNPZjY5NDhLd215Q1dieXJsZVkyTEJReGJodTFMZ2lqVXdVd1RTd2FpdEF1NFVFSEtldFdTcjBoQVF1U1YvZ29CUTN0WG10Zk5hSmZZcVZRc3g4Y3BmczlQTWVjMFNKQVF2ZytqT3F2VG1DN0VSZkRwTG9GTVFPMERrbFZXTXhFa0xEbnQiLCJtYWMiOiI0Y2M2OTg4ZmIzMTIyMzEzNTk0MDAxYjcwMTI5MGEyNTcxNDk3YzU4OWFhMzRjMTY4ODg1YTQ0Y2ZkYzgyZmRlIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1246378624 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246378624\", {\"maxDepth\":0})</script>\n"}}