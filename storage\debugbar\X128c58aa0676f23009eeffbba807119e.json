{"__meta": {"id": "X128c58aa0676f23009eeffbba807119e", "datetime": "2025-07-12 16:36:13", "utime": **********.304998, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338172.848486, "end": **********.305014, "duration": 0.4565279483795166, "duration_str": "457ms", "measures": [{"label": "Booting", "start": 1752338172.848486, "relative_start": 0, "end": **********.237961, "relative_end": **********.237961, "duration": 0.38947510719299316, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.237971, "relative_start": 0.38948512077331543, "end": **********.305017, "relative_end": 3.0994415283203125e-06, "duration": 0.06704592704772949, "duration_str": "67.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45710176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0118, "accumulated_duration_str": "11.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2686439, "duration": 0.01092, "duration_str": "10.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.542}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.288329, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.542, "width_percent": 3.559}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.294616, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.102, "width_percent": 3.898}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-170769638 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-170769638\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-984511356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-984511356\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1392045816 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392045816\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338164909%7C19%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilc5TFBqUFZUYVRXMkhjNy9DMzluYXc9PSIsInZhbHVlIjoiVzI4TWtzQzB0OE50MzRZUmxhQ203dHd4NHVxM3Z4akpwWW9uVWEvWGRWNGZhejdXNFdTN0E2VGhiZDZGV0JUc21XUDAzeFJvNzRZTVZxbVNFbTJmK1ZFTzN2TXRVNmNaRVVlcDBSTUVrTWsybjBHWGVtQjN1cllnZDhVZEdwZnJ0c1BoamtLa1lsNVNvRGpBTW5wS3VBYWRUNG1zQSsvMUNQcW5idGVyTnY1aHdkRDNIUEJLc2JtU0g0YzNmTHpBM0J0K3VtUU1RYmpKNzBvcFJSUHhadU1tOFVMZ091NS8xNGExMEVsUGZ1N0FobDlPNnF5dXh2V3FuR2JHTDBjQUtRV3l5THQ2VVIyMElTa29TOVVLcHU4YmhFNlptcFhHUzlJNCtBdVByUkNBUVhZOTNlTTE4emU5dU0wSktQL3FCZEM5bk93eThYSk52U0cxOHB1a1Q2ZnZqa1lQQ01aTWozS041ZVpXU282OG5QY1pxMmI5SFdDcDJJdHhMVVhhNCt5UUtDTElMSk14aUFtZHM1TkYyZ0NvUW01WEZTaVl4NWRtVEowREhGUC9NVUdKM3VZWmI4OGl0bnhPYkxlbEx0eVlBanRhd0Rrd2w5MjVvbVlkVXArbEx0UllQQU9FUHlhTTNqN0swaTJtdXo5NkZXaSs5eFlFTWJTQ0lhRSsiLCJtYWMiOiJlMDQ5YWExMjY3MDM5MTE3ZTc5NGVkZDcwYTVhZjg2OGI0N2NhOTMzZmQxNDNlYjNiZWU0YWIwMThiNzNkYzgyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik8yZGlSdXFjWnNidDJQa0JKZnVpdnc9PSIsInZhbHVlIjoiRGJaMXowemI5eDRZL2x3OSs3UlZLUDdXZ1dHNzhKeWJzTXhrWmxJVVZZVitpeU9FTlFOcXc2cTEwRnhsYnQvK3NPREl3Rk9GdnVnMFhVZDBUR1JtZnBac3JSV3I4bFlidlYzOWZDYVBqbCtCcmVINWtoY1NtaWpUN1JRamJJeEpyMU04TWV5SUhjQU4rOUNKUkU2ZWhEVXd4L1FlM0IvUFhJUFgrajNwNVV2M3UzQmwvQ04vSmNkbGNTSml3Si9KVHNLZXR2V0llNk5iNWlKRHBmb2pOQWVDamM4OWN4QnZtK2RDdktiMWxTVFlIeEMrc0FKbVZOK0RWQlN5VUc1cUlBUGtGR3ZEeTFyeUY0SFEyV3lBblVZU2YwaUxZWE1GTlhpck5DWFg4WlZZcXdvV0lNZi95QUhEVUtHb0p2TWJOcm1xc0REUURaSm1UbVNoYWMyMmtXVWxQbmNLNjBNRDlaRzBlN0UrdzdVY3huZDh0bFNGNW1mZVlTWjdMMjZHcFFTYTBTWnFNUkZXS3J6YXh4akdzY3A4ZXFxc0RuMDd4MVp1a2I0WE5ycEN0OGRUdG5hV0MvNXRVK3Erd0t0VDlaUFRRaWJaMUlWQnRYM25WRWZUZkREWXZZQUU4QlY4QUpWbkJwOUtHYmFOclFJTXg3NjNaamVvdEx5bStONDgiLCJtYWMiOiIwZjk5MGU5YTBiYmEyMGRmMmU2YjgzMmMwMzRkM2EwY2VhYWYwMDBiNDZhMjJkMmM4MDU0YTI2NmExYmJjYWM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-173854179 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173854179\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZUSkEvU1Q2cmtGa3IvV29oNTg4NUE9PSIsInZhbHVlIjoicHN5ZXRPaVozTjhuL0ZLL0NNQ3V6UUtsbTZsQjNYc0xzb2dMdkRLcHdXcTR6VGw2SkhQZTM5MzRXeVc2U2V2cVc5eFNTNElRcmdMYnIrVTRnZ2xmK0V3T0I1TDR3dTZKSDV4bTFDV0hyck9adXpWRGRFVEROTURYWEF2MXVRZTAzamh1c3ZaQzJTMlBpKytITGxjU0pocHE0SExQU0VIWnhNdjdvUnFxZk5yNGZtVDBWeHF3QnhJeVgrSU5CaDdZRmhJUEpWbkErQlJ1b3l5ZVFyZWxDOWlPaU95NlRTTExZUS9yd0lFMFVTa3dlTTlrRXJwVFd1MVNmanM4MjJkOGFNaXRyeHRvMit4VHJHSDc5Z2FkdUZGcUw2d2MvcE9xYjU3ckdDd3dGc2o1TUNPMktNMjM2ZERtaFZkRmJwODduQ05aZmtGQmE0WVdWSnpXNnJWOUw4K2IxNTM1MnY0TytPcm5kQVJ3ZnpxcVoxSHR0ZTU4S2pRSm1hVkUxdUJJd3gwbzg0d3hXL3FCV3pHL3pzRWI5K2pDdVdWcEtMRWdBQU1BYUZobW9nT2pxM3hwYjBVNFVORDhEQWxSUzNxWmM4S1lXVzVIS1JJbnhLWXVucVFBNWw4OTRFSjRpZ3YweGxoMUFxdFVKOHY2TnU4QU1NcXlIa0xCYW1hS3VDZlkiLCJtYWMiOiIzYzYyMDU1ZTk4ZjFlYmE1ZDkyOGMwYzIwYzhlMzc3YTE3NzI0ZjVlZDdmMTAzODM2Mjg1NjlhMDA2NzY4MjAyIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpUb0hYR0Ryc2N6TVZNNE9CdEtMUUE9PSIsInZhbHVlIjoiOEttUjZnYXN0ME9NV2Rwd2hBK0JIeE8rajdRYnZ2VDBXTU9NRWpmYTVUTzErejc3RDRvSDdkaHc2dFNLOTdkS3laN0x0UFRXWUpJWFhTdzYxVjBWdUtxa2FyanpITUdLcS9Rb2dIYlNNdCs1MStXalFpT0FnMFoyK2pnaWwzYlpESVdpM24wV2UzaU9pM0NsbWRtWWlUbGZiQVBzZ2xkNExsOWlhVUpLMDljY2dmaWM0YjV4WThMWTVmdVBqUSt4ZXlFNWlEdWxhVUNieW5VbkQvZFFLRlRVTkpveUpGZ2NzVFBHcTZkUDNsYmVHYnlEMnJIWnk0SzM3MEoxUStaNVA1Y1AyR2hpdFNJaUtCOTY1SVl0d0doU212VWFxMmF6UHdPVGNWQW9OcjMxemVlT2g3MjB5Q2h2MzEvZGxyYXE0WHUydHdlQ0s1c0NSUjlvWUZxeVZ4NmlSMGd1bWJMdkx6OWFlZjhaZnliMi91aUhsb012L2RYTFJYN1c0REszRTk1L0hBUXFuanJWaElkcmpmb295d0JoRjBTQmwxR1N4eHozT2VxRkZkWmR2N0g2K2doRE1qdmVxN2NEc3pwOGNMWWRsT3VUeFpCMVhCMkJVSDlRemZjaHBpdlY5RWc0NlVRUFdINnJIWHhuc3hMQndqd0FNbzQwZG5Fc3VkeTEiLCJtYWMiOiJmZTlkODM2NmVhNTFhMGZkMzk5NDg0NmIwODYxYjAyOGM0NDViNWI2OTlkOWZmZGZhNGUyOTJmNjBjZDczN2VlIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZUSkEvU1Q2cmtGa3IvV29oNTg4NUE9PSIsInZhbHVlIjoicHN5ZXRPaVozTjhuL0ZLL0NNQ3V6UUtsbTZsQjNYc0xzb2dMdkRLcHdXcTR6VGw2SkhQZTM5MzRXeVc2U2V2cVc5eFNTNElRcmdMYnIrVTRnZ2xmK0V3T0I1TDR3dTZKSDV4bTFDV0hyck9adXpWRGRFVEROTURYWEF2MXVRZTAzamh1c3ZaQzJTMlBpKytITGxjU0pocHE0SExQU0VIWnhNdjdvUnFxZk5yNGZtVDBWeHF3QnhJeVgrSU5CaDdZRmhJUEpWbkErQlJ1b3l5ZVFyZWxDOWlPaU95NlRTTExZUS9yd0lFMFVTa3dlTTlrRXJwVFd1MVNmanM4MjJkOGFNaXRyeHRvMit4VHJHSDc5Z2FkdUZGcUw2d2MvcE9xYjU3ckdDd3dGc2o1TUNPMktNMjM2ZERtaFZkRmJwODduQ05aZmtGQmE0WVdWSnpXNnJWOUw4K2IxNTM1MnY0TytPcm5kQVJ3ZnpxcVoxSHR0ZTU4S2pRSm1hVkUxdUJJd3gwbzg0d3hXL3FCV3pHL3pzRWI5K2pDdVdWcEtMRWdBQU1BYUZobW9nT2pxM3hwYjBVNFVORDhEQWxSUzNxWmM4S1lXVzVIS1JJbnhLWXVucVFBNWw4OTRFSjRpZ3YweGxoMUFxdFVKOHY2TnU4QU1NcXlIa0xCYW1hS3VDZlkiLCJtYWMiOiIzYzYyMDU1ZTk4ZjFlYmE1ZDkyOGMwYzIwYzhlMzc3YTE3NzI0ZjVlZDdmMTAzODM2Mjg1NjlhMDA2NzY4MjAyIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpUb0hYR0Ryc2N6TVZNNE9CdEtMUUE9PSIsInZhbHVlIjoiOEttUjZnYXN0ME9NV2Rwd2hBK0JIeE8rajdRYnZ2VDBXTU9NRWpmYTVUTzErejc3RDRvSDdkaHc2dFNLOTdkS3laN0x0UFRXWUpJWFhTdzYxVjBWdUtxa2FyanpITUdLcS9Rb2dIYlNNdCs1MStXalFpT0FnMFoyK2pnaWwzYlpESVdpM24wV2UzaU9pM0NsbWRtWWlUbGZiQVBzZ2xkNExsOWlhVUpLMDljY2dmaWM0YjV4WThMWTVmdVBqUSt4ZXlFNWlEdWxhVUNieW5VbkQvZFFLRlRVTkpveUpGZ2NzVFBHcTZkUDNsYmVHYnlEMnJIWnk0SzM3MEoxUStaNVA1Y1AyR2hpdFNJaUtCOTY1SVl0d0doU212VWFxMmF6UHdPVGNWQW9OcjMxemVlT2g3MjB5Q2h2MzEvZGxyYXE0WHUydHdlQ0s1c0NSUjlvWUZxeVZ4NmlSMGd1bWJMdkx6OWFlZjhaZnliMi91aUhsb012L2RYTFJYN1c0REszRTk1L0hBUXFuanJWaElkcmpmb295d0JoRjBTQmwxR1N4eHozT2VxRkZkWmR2N0g2K2doRE1qdmVxN2NEc3pwOGNMWWRsT3VUeFpCMVhCMkJVSDlRemZjaHBpdlY5RWc0NlVRUFdINnJIWHhuc3hMQndqd0FNbzQwZG5Fc3VkeTEiLCJtYWMiOiJmZTlkODM2NmVhNTFhMGZkMzk5NDg0NmIwODYxYjAyOGM0NDViNWI2OTlkOWZmZGZhNGUyOTJmNjBjZDczN2VlIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-334615313 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334615313\", {\"maxDepth\":0})</script>\n"}}