{"__meta": {"id": "Xeb786003a02340a9b6064e72f49b2648", "datetime": "2025-07-12 16:35:05", "utime": **********.595281, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.166382, "end": **********.5953, "duration": 0.42891788482666016, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.166382, "relative_start": 0, "end": **********.53816, "relative_end": **********.53816, "duration": 0.3717780113220215, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.538171, "relative_start": 0.37178897857666016, "end": **********.595302, "relative_end": 2.1457672119140625e-06, "duration": 0.057131052017211914, "duration_str": "57.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45724888, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027199999999999998, "accumulated_duration_str": "2.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5703359, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.279}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.580557, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.279, "width_percent": 17.279}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.586356, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.559, "width_percent": 15.441}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1501010163 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1501010163\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1162165717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1162165717\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-837114725 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837114725\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264296673 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338102187%7C14%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUrMFZseU90MzlZa2Rtbkt5cndhMnc9PSIsInZhbHVlIjoiWGR0V3ZZenVrS1k1allObVNYaUxpVk50WTFNUllBcUl5VXhoSEJPUjdDN0NXb2U0dDdiY1REaU04NmtSai92ejhrYjhPaERFbWpNcWpYVytBRm9iYnY2YnhaN1lQcE5pMWIrWWFWbGlHdDc0bVdyTy9ySlBFVUlPb0dmcGxoV0RUd0QrTStZRkg2Qnd1STZlbWFXNEFHRjVIMldJaXdLR2Nlb25UR2w5MUg0Y0t2OU5XVlMySmJhUGVvV08ya05BVmd0RG5OOTVjcnYyQ0d5ZE5vaFB2dm9pckJKeTVrN0dlWVQ3cHZsendiL3F0elpUMG5abkp6ZHl3aEhJUVJkdi9jMFlOT0RyZEU3elc2RDM4blVGemZaTCtIcXRyV3dhczNTYThQbUJLY3dkUEZweGRHRGRiTG95eFMyZ1drOXg3Q3d1TldqU25CaS9nY1hiQlM1bGFQYjZlOXBkcWFCb1NzUzRWY0wzM1JDVld2ZEY4dnRLWS9VSXNYdDJsS3A4OVNJWUVwWVhpa1ZyTG1xd2VGWkd3K3ZHM1ZocWtRRnlSbDNNdFFSdkg2ckxVeC9FalJBK3piTzVhVWlGc0IvYTVWNTVKYUFycUZTNW9hQUxQUHdXZ0RveXBjRzRHQmZMV3lHdVB6NzB0UjhOSDJFTTFmQ1huTko1cXUrc24wWksiLCJtYWMiOiI2NTE0NDgxMTY4NjVkM2VlZjk4N2JmNDc0YjIyNzViMWRiZmM4OGQ2ZmNjMjM2MTM4MzA3ZDJhMzY5M2ZmMzg1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikh4SE1BTHJ5SDRtanJZbmRDZXl1WHc9PSIsInZhbHVlIjoiWEcwT2tJOCtMTkZUeXNYQWFTcW5oQjBPOVZyNEdzaWI1YUQ3OHAwbXdJdFN5djJaeUtzVjJnUFIvQ2NzZHFPRWoxTENybE9RN2hzWC9JbVRaenozOStEaW0ycUlBdGY4WklsWUliRW1DOXFEVTUrTEpIL0lkNHVkaGthSWJWQ0Q3UDgrTkZSVmRkNFZubCtoT1BiL1JQckZER3ZCeFpCTDRvV3VWUTlWaFNKMk5sNFZXWFUrYnM5aTVSM1RZc1R2aXFZNG5LZ1pOd3E2MTRkNzA0b0NNVHZNVHI4dkdUVmU1ZE5sNXBTQVo1TWlqZDlvSVdGOGRrRXFOTlhzS3VUelQ1ZEdDQVM5U2FRVnEvWG5MdGFxVnViL3FNR2E0VmV4ckMrOU5tM2VEM0hRWGJwa2hGVFlyeFdKRi80UGk1eHBmZ1p4L1hweWVmREswVE93NEpFcmVjV2xqVzlSUVRVNWt1STV0cmFreXFUNXFpVGNQWlZFQ2NEYjFOMzkxeVVLVzUzSHpJRjdWUVBFckd5a3grL1ZDL2N2aFNhTVFBRGpMWTJTVFBHWUF0NnYyM1hLRmJQQk55dGczSkNkNlZXbTdwYVZtNTNDOWkvL1FEM1FpK1RpcmlVWEJ2SVdWUmliN3p1SURCeUl6NjVEL0ZDYkZtYmNjeGJUaG1SNmlud3UiLCJtYWMiOiI5MTRhYWUyNDE2M2ZjZmZkNmE5NjFkZmNlZGEwNTQ3NjVkNDlhNWQwNTgyZmYzZDJmN2FhMmM2YWQ0MDdjNDIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264296673\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-451494057 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451494057\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-813971712 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:35:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikh6ZmVObVJwL2NiZXdPcFhyRm44aUE9PSIsInZhbHVlIjoiVGFIRGJoMWhCNS9uQW41blcwWXRJNzJSTHBOZ3hIay9tQUh2YXBNQllaWGY1RlJOakVEeTNVSzNUaFVnWGZDczdVT1drUXpWdUxTOHBjdnBSdGhabEpZM29JSDR0T0ZHUXM0bUJkbEd2YnQ2cXZ2eWtnaUhYT3RpNk9YbUZzSGRlQlBMZmcydHQzWHVvaWx0UFdzLzBET0xvRjdSa1dkQ0Z5a1pzSUg3UzFBelcyT3h0SGpUMzhLODlwalYvZTR2U05CT2c0azN6N25abUtVc2hvYUlMY0hoOWIxcy9xb0I4MkpJWFIyTkdTSkg0SktURkpIMnJybzY2T1dCaElWUTRzc2VkVGJqaHNWZG83djNQbklVL0poRWNlUVRwTUJGZlVTbW9qTW90aVBVQ1NNeWFFekJCc0pISTR1UkZ1blpIT2NvNmt5TDcyUjNSQlJvRXpZbFRnOGQvOGkxMnJITk1pZTk4K1ZCWkNVQW1JQXpnWGlqYUcxNWxKZmpKQjF3dE9BaGVhRTlobks1LzJ0V1dqL0RZZlJOTm9nRnhrTEdXUFJEREFFait4T1hwR2tSYWhWZ0sxMVlxVnZhNHUzZFA2UC9Qc3ZYd2UvV2c0Z0Z0cnVrcEJURTFpQmg1SVdHUkxTSE4xU0Rsc0syUngrL0dPdytlZXFSUlNNL3lSREIiLCJtYWMiOiIyZDg1NTdkODI4MTZkMzExYjhlYmE2NGU5NTJmYzE1MDVjN2M0MGQ4YjczY2RmNzEyZGUyMzJhMGYwNzg2NTIzIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFEbklQZlVMR2E5Q0FLd1BVajh3Z0E9PSIsInZhbHVlIjoiTFdJS3ZQcUhIVWdlckhIZlY5dnV5d25IUnRJdTVWbXcxQTI4dS9Mbmx4KzVsbDFwS3ZLeStaWEQ0aWlRdjdmZERXeW83Tyt2N1kySzd4SGxJREhaZWJWYzZvS3p4OHcvMURldnk3LzB4OFJySmd1WW91UVczSTVseVp6a1U3QVR1bTVUdGdlN0lsTkl0OFJSZjg3OGhJM0d5bkMzWlRyTnBoYkw5WGdXVmZxTnAxNnAyN1FtaktsOFQ5ZHd3aTZteXF4eXMvNGs4RTRSRmlTakx4V0NEYVh3SDB5VmlWRGJGVGxmMndkOS9tQUFEZzVFM01wOVBjZ1RnSmxYK1g3dTc1LzludkFOZzZ3bndzMXZJdjU4MHdMK1A2dW83aUFqTTVEcUl3WTNQUi9kOHBOZ0pWL0lLRjJBbGRBNmpiMlhsSzhlVmRzUktQYXRHa3NWWWdxMG4wd2k0LzRUS3dnK1F0SkdLQmxPMkxhRkZtVE1Zc2k1K1hwdHBuaXo1UUJzR0JQTXh6OUZLYlJ4WnZ2TmsvU1Yza0o3bFpVUXY0SVNzSVNvN0hoMTAwZEI2TjREbjdXaFlEN3dBSTFqak1sWlV1Y0M4KzFxUDhITUdoaU8rR05rNUx6K2FjejlmeDE4NDFGRUpFTTY1Tk9lWFJvUC9nSVErZk12b2VXdlRFbGQiLCJtYWMiOiJiZGU4ODNjNDVjYzk3YWIyYjk3M2RiOTBiNDQ4MTc4ZmEzOWI1YmIzNTFkY2Q5YjI0MTE5YTBmOWZkNzNhMmJmIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikh6ZmVObVJwL2NiZXdPcFhyRm44aUE9PSIsInZhbHVlIjoiVGFIRGJoMWhCNS9uQW41blcwWXRJNzJSTHBOZ3hIay9tQUh2YXBNQllaWGY1RlJOakVEeTNVSzNUaFVnWGZDczdVT1drUXpWdUxTOHBjdnBSdGhabEpZM29JSDR0T0ZHUXM0bUJkbEd2YnQ2cXZ2eWtnaUhYT3RpNk9YbUZzSGRlQlBMZmcydHQzWHVvaWx0UFdzLzBET0xvRjdSa1dkQ0Z5a1pzSUg3UzFBelcyT3h0SGpUMzhLODlwalYvZTR2U05CT2c0azN6N25abUtVc2hvYUlMY0hoOWIxcy9xb0I4MkpJWFIyTkdTSkg0SktURkpIMnJybzY2T1dCaElWUTRzc2VkVGJqaHNWZG83djNQbklVL0poRWNlUVRwTUJGZlVTbW9qTW90aVBVQ1NNeWFFekJCc0pISTR1UkZ1blpIT2NvNmt5TDcyUjNSQlJvRXpZbFRnOGQvOGkxMnJITk1pZTk4K1ZCWkNVQW1JQXpnWGlqYUcxNWxKZmpKQjF3dE9BaGVhRTlobks1LzJ0V1dqL0RZZlJOTm9nRnhrTEdXUFJEREFFait4T1hwR2tSYWhWZ0sxMVlxVnZhNHUzZFA2UC9Qc3ZYd2UvV2c0Z0Z0cnVrcEJURTFpQmg1SVdHUkxTSE4xU0Rsc0syUngrL0dPdytlZXFSUlNNL3lSREIiLCJtYWMiOiIyZDg1NTdkODI4MTZkMzExYjhlYmE2NGU5NTJmYzE1MDVjN2M0MGQ4YjczY2RmNzEyZGUyMzJhMGYwNzg2NTIzIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFEbklQZlVMR2E5Q0FLd1BVajh3Z0E9PSIsInZhbHVlIjoiTFdJS3ZQcUhIVWdlckhIZlY5dnV5d25IUnRJdTVWbXcxQTI4dS9Mbmx4KzVsbDFwS3ZLeStaWEQ0aWlRdjdmZERXeW83Tyt2N1kySzd4SGxJREhaZWJWYzZvS3p4OHcvMURldnk3LzB4OFJySmd1WW91UVczSTVseVp6a1U3QVR1bTVUdGdlN0lsTkl0OFJSZjg3OGhJM0d5bkMzWlRyTnBoYkw5WGdXVmZxTnAxNnAyN1FtaktsOFQ5ZHd3aTZteXF4eXMvNGs4RTRSRmlTakx4V0NEYVh3SDB5VmlWRGJGVGxmMndkOS9tQUFEZzVFM01wOVBjZ1RnSmxYK1g3dTc1LzludkFOZzZ3bndzMXZJdjU4MHdMK1A2dW83aUFqTTVEcUl3WTNQUi9kOHBOZ0pWL0lLRjJBbGRBNmpiMlhsSzhlVmRzUktQYXRHa3NWWWdxMG4wd2k0LzRUS3dnK1F0SkdLQmxPMkxhRkZtVE1Zc2k1K1hwdHBuaXo1UUJzR0JQTXh6OUZLYlJ4WnZ2TmsvU1Yza0o3bFpVUXY0SVNzSVNvN0hoMTAwZEI2TjREbjdXaFlEN3dBSTFqak1sWlV1Y0M4KzFxUDhITUdoaU8rR05rNUx6K2FjejlmeDE4NDFGRUpFTTY1Tk9lWFJvUC9nSVErZk12b2VXdlRFbGQiLCJtYWMiOiJiZGU4ODNjNDVjYzk3YWIyYjk3M2RiOTBiNDQ4MTc4ZmEzOWI1YmIzNTFkY2Q5YjI0MTE5YTBmOWZkNzNhMmJmIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813971712\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1594294859 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594294859\", {\"maxDepth\":0})</script>\n"}}