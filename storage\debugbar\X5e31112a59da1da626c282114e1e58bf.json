{"__meta": {"id": "X5e31112a59da1da626c282114e1e58bf", "datetime": "2025-07-12 16:35:05", "utime": **********.624333, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.165382, "end": **********.624347, "duration": 0.4589650630950928, "duration_str": "459ms", "measures": [{"label": "Booting", "start": **********.165382, "relative_start": 0, "end": **********.540723, "relative_end": **********.540723, "duration": 0.37534117698669434, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.540732, "relative_start": 0.3753499984741211, "end": **********.624349, "relative_end": 2.1457672119140625e-06, "duration": 0.0836172103881836, "duration_str": "83.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45349528, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02743, "accumulated_duration_str": "27.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.577168, "duration": 0.02609, "duration_str": "26.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.115}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.613205, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.115, "width_percent": 2.698}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6164088, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.813, "width_percent": 2.187}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-863025474 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-863025474\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-818308460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-818308460\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1380848440 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380848440\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1927700794 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338102187%7C14%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkUrMFZseU90MzlZa2Rtbkt5cndhMnc9PSIsInZhbHVlIjoiWGR0V3ZZenVrS1k1allObVNYaUxpVk50WTFNUllBcUl5VXhoSEJPUjdDN0NXb2U0dDdiY1REaU04NmtSai92ejhrYjhPaERFbWpNcWpYVytBRm9iYnY2YnhaN1lQcE5pMWIrWWFWbGlHdDc0bVdyTy9ySlBFVUlPb0dmcGxoV0RUd0QrTStZRkg2Qnd1STZlbWFXNEFHRjVIMldJaXdLR2Nlb25UR2w5MUg0Y0t2OU5XVlMySmJhUGVvV08ya05BVmd0RG5OOTVjcnYyQ0d5ZE5vaFB2dm9pckJKeTVrN0dlWVQ3cHZsendiL3F0elpUMG5abkp6ZHl3aEhJUVJkdi9jMFlOT0RyZEU3elc2RDM4blVGemZaTCtIcXRyV3dhczNTYThQbUJLY3dkUEZweGRHRGRiTG95eFMyZ1drOXg3Q3d1TldqU25CaS9nY1hiQlM1bGFQYjZlOXBkcWFCb1NzUzRWY0wzM1JDVld2ZEY4dnRLWS9VSXNYdDJsS3A4OVNJWUVwWVhpa1ZyTG1xd2VGWkd3K3ZHM1ZocWtRRnlSbDNNdFFSdkg2ckxVeC9FalJBK3piTzVhVWlGc0IvYTVWNTVKYUFycUZTNW9hQUxQUHdXZ0RveXBjRzRHQmZMV3lHdVB6NzB0UjhOSDJFTTFmQ1huTko1cXUrc24wWksiLCJtYWMiOiI2NTE0NDgxMTY4NjVkM2VlZjk4N2JmNDc0YjIyNzViMWRiZmM4OGQ2ZmNjMjM2MTM4MzA3ZDJhMzY5M2ZmMzg1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikh4SE1BTHJ5SDRtanJZbmRDZXl1WHc9PSIsInZhbHVlIjoiWEcwT2tJOCtMTkZUeXNYQWFTcW5oQjBPOVZyNEdzaWI1YUQ3OHAwbXdJdFN5djJaeUtzVjJnUFIvQ2NzZHFPRWoxTENybE9RN2hzWC9JbVRaenozOStEaW0ycUlBdGY4WklsWUliRW1DOXFEVTUrTEpIL0lkNHVkaGthSWJWQ0Q3UDgrTkZSVmRkNFZubCtoT1BiL1JQckZER3ZCeFpCTDRvV3VWUTlWaFNKMk5sNFZXWFUrYnM5aTVSM1RZc1R2aXFZNG5LZ1pOd3E2MTRkNzA0b0NNVHZNVHI4dkdUVmU1ZE5sNXBTQVo1TWlqZDlvSVdGOGRrRXFOTlhzS3VUelQ1ZEdDQVM5U2FRVnEvWG5MdGFxVnViL3FNR2E0VmV4ckMrOU5tM2VEM0hRWGJwa2hGVFlyeFdKRi80UGk1eHBmZ1p4L1hweWVmREswVE93NEpFcmVjV2xqVzlSUVRVNWt1STV0cmFreXFUNXFpVGNQWlZFQ2NEYjFOMzkxeVVLVzUzSHpJRjdWUVBFckd5a3grL1ZDL2N2aFNhTVFBRGpMWTJTVFBHWUF0NnYyM1hLRmJQQk55dGczSkNkNlZXbTdwYVZtNTNDOWkvL1FEM1FpK1RpcmlVWEJ2SVdWUmliN3p1SURCeUl6NjVEL0ZDYkZtYmNjeGJUaG1SNmlud3UiLCJtYWMiOiI5MTRhYWUyNDE2M2ZjZmZkNmE5NjFkZmNlZGEwNTQ3NjVkNDlhNWQwNTgyZmYzZDJmN2FhMmM2YWQ0MDdjNDIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927700794\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1722531663 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722531663\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1875065116 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:35:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhkSm5UVUlJbjRlRzF1dmpxWkNWdUE9PSIsInZhbHVlIjoiUitrQ0lJU3hMcFRmWUFaakhUVzFURjNaTlFDL3VPY0g5UEt6UnBBUVNyeHdLVmFXenZHa3ZaSGRLUGFVcEhUMnBqdnNTVWViOFBjMHdsQi9sUmUzUzkvVTZKUHo5bnJIMmFOaEZiYVhFenJpbVJqcnE0NmdRRFZwelB6M25xUlpvck5JM2pydmNFNDdGRWkzUnM0RVZFVVU2SHdFZWJPRjhLb3RzeW00ZjNXQ21NVmlDNmxpN0JzVm1IR2JjWjN3dUZjcE5GekFGV0tzL0ZiSzR3d1VjZUM4Z0Z5b1lYcFI4L29zemM4aEdLcVlvSzVWeHpGbUlkcTFVVTJLVzNBZ0ovWkZjdUtDUVZ4Y3g3WlEwZU00RFJTS2d5VzBmMzJiSTlGOWVFNlJabnMwVmRua3ZaMmlCWlJKanVoY2diMGFuVnp1dkg3ZkpMdmJoOW9MTlhhUFkwZnVvSjFvUEJVUEI1eC9md0FzWjdMR3hYTjJ6UkFObHE4cDFHeWZqWFVyeUZQdVVnMVoyanYrUTJHd1lobXJ2cUVmTVVIWHloNlZLWXV0NnZjY0VCVVZTaVRwRitPWENqUWp3YlBDSC8xOFY5bm1iTkVHM09tUnM1aGdGU0xEdjRhai9qakhHMXBYREVwU3JCOE16d0NnZE9qd2ZYTFI1OURsVWhMd25DeC8iLCJtYWMiOiIyZTcxMWMxMjhmNDc4YzA4NTllN2I1MmM1ZTg5NWZkNGM0YmRiYTE3MzA2NzczMjUyMjgzZjZiZjUzYmIzYzJkIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBOUkJpa0EvOG9QOHNoS2NTU3Fnc2c9PSIsInZhbHVlIjoiMFdQZ1NpYXlpS2pibmFNb1d2OUcxYmpqbWIzc0RvMCtMM0xqamdPL3NIeDAvcDk3R2w2dTgrd00zZUsrWEh1MS9TT3YyV01qVVNZbWNHNGdKNGRjdFUvZ3VNUW1USlpXa01yWjlJRDRnOVFIeXBaTjBoK1FpejVCUzltUUd0RVR5UDNtQk5lZndSK1ZrMjc3VjVFZGxGcTlEN2phQUV5SzgyWU8wS3ZiMDZDd1M0YllETUlyQjFGbS9hd29wUkNacWFRVVR0WXBTekNUSDVPbFhwb2d3UlNsYklwWnJiYm9rUkZ3ZURQcm9SSUdqZU1neHUzU2FvdjdqNTFYaHptTzFPaThlUWkvM1lqNm1VUHNSN1FEK3AvT043VkltdmxyaTJjQk50d1RPeWtmTFRSdWxMWmdDM25taVNNSTlXYUFweEVnaC8ya21nWGUybzlmZHhQeEphQmlTL2xkM1VaRC8vRFU1K3pJVHRlZTMxYjRLcld1VmhRZFNnVTMwM2RDZlBIblNOSzY5VVZOR3k4Y0xKUGd3T0NINDFRUVpueW5VeGpDWmgwQWlDb0xzRm1zbElKcUZ3eGhrcDJkc1NtZG9WMlk2eFB5eGV4Yk5GTUxTT0V3TCtmdXBDeDNjTmpjZUFrMnpPSC96a2U3LzdkY200akliSWpyK1I0Q2plckoiLCJtYWMiOiJhODBmMmMzZjAxY2QwYjM5OWIxMTk5NDQ2ZDZmZjM2MzUzYjM2ZWNiYjZiYjExNWI4ODRlMGQxYWU1OTdhMzE4IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhkSm5UVUlJbjRlRzF1dmpxWkNWdUE9PSIsInZhbHVlIjoiUitrQ0lJU3hMcFRmWUFaakhUVzFURjNaTlFDL3VPY0g5UEt6UnBBUVNyeHdLVmFXenZHa3ZaSGRLUGFVcEhUMnBqdnNTVWViOFBjMHdsQi9sUmUzUzkvVTZKUHo5bnJIMmFOaEZiYVhFenJpbVJqcnE0NmdRRFZwelB6M25xUlpvck5JM2pydmNFNDdGRWkzUnM0RVZFVVU2SHdFZWJPRjhLb3RzeW00ZjNXQ21NVmlDNmxpN0JzVm1IR2JjWjN3dUZjcE5GekFGV0tzL0ZiSzR3d1VjZUM4Z0Z5b1lYcFI4L29zemM4aEdLcVlvSzVWeHpGbUlkcTFVVTJLVzNBZ0ovWkZjdUtDUVZ4Y3g3WlEwZU00RFJTS2d5VzBmMzJiSTlGOWVFNlJabnMwVmRua3ZaMmlCWlJKanVoY2diMGFuVnp1dkg3ZkpMdmJoOW9MTlhhUFkwZnVvSjFvUEJVUEI1eC9md0FzWjdMR3hYTjJ6UkFObHE4cDFHeWZqWFVyeUZQdVVnMVoyanYrUTJHd1lobXJ2cUVmTVVIWHloNlZLWXV0NnZjY0VCVVZTaVRwRitPWENqUWp3YlBDSC8xOFY5bm1iTkVHM09tUnM1aGdGU0xEdjRhai9qakhHMXBYREVwU3JCOE16d0NnZE9qd2ZYTFI1OURsVWhMd25DeC8iLCJtYWMiOiIyZTcxMWMxMjhmNDc4YzA4NTllN2I1MmM1ZTg5NWZkNGM0YmRiYTE3MzA2NzczMjUyMjgzZjZiZjUzYmIzYzJkIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBOUkJpa0EvOG9QOHNoS2NTU3Fnc2c9PSIsInZhbHVlIjoiMFdQZ1NpYXlpS2pibmFNb1d2OUcxYmpqbWIzc0RvMCtMM0xqamdPL3NIeDAvcDk3R2w2dTgrd00zZUsrWEh1MS9TT3YyV01qVVNZbWNHNGdKNGRjdFUvZ3VNUW1USlpXa01yWjlJRDRnOVFIeXBaTjBoK1FpejVCUzltUUd0RVR5UDNtQk5lZndSK1ZrMjc3VjVFZGxGcTlEN2phQUV5SzgyWU8wS3ZiMDZDd1M0YllETUlyQjFGbS9hd29wUkNacWFRVVR0WXBTekNUSDVPbFhwb2d3UlNsYklwWnJiYm9rUkZ3ZURQcm9SSUdqZU1neHUzU2FvdjdqNTFYaHptTzFPaThlUWkvM1lqNm1VUHNSN1FEK3AvT043VkltdmxyaTJjQk50d1RPeWtmTFRSdWxMWmdDM25taVNNSTlXYUFweEVnaC8ya21nWGUybzlmZHhQeEphQmlTL2xkM1VaRC8vRFU1K3pJVHRlZTMxYjRLcld1VmhRZFNnVTMwM2RDZlBIblNOSzY5VVZOR3k4Y0xKUGd3T0NINDFRUVpueW5VeGpDWmgwQWlDb0xzRm1zbElKcUZ3eGhrcDJkc1NtZG9WMlk2eFB5eGV4Yk5GTUxTT0V3TCtmdXBDeDNjTmpjZUFrMnpPSC96a2U3LzdkY200akliSWpyK1I0Q2plckoiLCJtYWMiOiJhODBmMmMzZjAxY2QwYjM5OWIxMTk5NDQ2ZDZmZjM2MzUzYjM2ZWNiYjZiYjExNWI4ODRlMGQxYWU1OTdhMzE4IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875065116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-199953204 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199953204\", {\"maxDepth\":0})</script>\n"}}