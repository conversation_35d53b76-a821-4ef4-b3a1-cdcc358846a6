{"__meta": {"id": "X472b001b864d7859d335f8919872d0e8", "datetime": "2025-07-12 16:36:00", "utime": **********.720023, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.250167, "end": **********.720038, "duration": 0.46987104415893555, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.250167, "relative_start": 0, "end": **********.648086, "relative_end": **********.648086, "duration": 0.3979191780090332, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.648094, "relative_start": 0.39792704582214355, "end": **********.720039, "relative_end": 9.5367431640625e-07, "duration": 0.0719449520111084, "duration_str": "71.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45364264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02164, "accumulated_duration_str": "21.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.678439, "duration": 0.02071, "duration_str": "20.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.702}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7083108, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.702, "width_percent": 2.68}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.712832, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 98.383, "width_percent": 1.617}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338115835%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZhTk1EbXBCZWxvQUR5Y2FmQkxodXc9PSIsInZhbHVlIjoiVjBEb1dIVGJqNU5uVGEzeHBDSHNrVkF4TlY0eWh6UlpQNkF1dDhBOC80TVp1VXF0M1JXRDN5YVlYWUtNWTFqTTk5eVMzZlRyUHRYaDBDTVQydTVxTmNrSWt1ZTBSNzg1UFl1VDhaQTlQTHQ3amVUOTd6UklmOTE3SVo5NjFWOUdLUmQweEhrdjBLcTU3NTRkaUc4djMvbE9QVFpyUjRTUlVCSTFYL01EVm5Ic3ZqamZFWWxCVmE5QytwZVdvWTdDYnB1a2owRStaYi9GSjdoeU52SFhsSUJWSkFYenlUdCtJUHVFbzBhQzYxMDNaakxLR3FMS1hmbU5sOCsvQVc4WjFsck94eVR6RFlaYmF4ZnFIMWgyTnpxMUlqelZLc2ZiVHJ0a2NEN1VKREZmNmRaTHVZMURRUm14c00rckxyTnZUdVNhbjdjYmQzemlaTkJueHR4SEVwaEVGUkFPM0ZySkNndVdiZDEya2lNRFIyWjh0N3FvSUFZT2RtTklDS1pjY2FocndiUDVXUWM1aWUrNWVGMHhHd2VuOUp0dzhiZUQ1RFgzVEUyMzl1anNBaWlCSnYvaG5nU3FDVWJuZWdDL0lEV2VhL2U3ZnJWV0hNbFZWVlk2SWkzdm52SlpURHYvZThUU2UyOWNIYUQ1Z2hxamxhaE5XREhqZS9GTUdGaHUiLCJtYWMiOiI1OTY3NDM4NmFmN2NlYjc3MjE5NGNmYzFjN2M5NTNmM2M0OGQ0MTQwN2Y4ZTdhOTYxN2Y0YmQ3MmZjODMwNmZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdydUlFb0lOV0dWdlZ5V3ZYWHN0MWc9PSIsInZhbHVlIjoiZ0tsRHJnRUZUZUcyYzRLbmZBaFpLYXA3azZweUU4UEZVQTJ3YXdCY3RRZ0JVSEtvK3pnZCtWaVRUbXFyK3B1T1FWdW1OdUpET0lSbSs5dFp4WFZrVUxrU1dIWjBDOUVxZlJrSTRlNGNqNHErVFMzQUQybUVFYmh4WVIwY2hPQ2tVVUVKQWRkMzhpNzhYUWU4TjdtSVBaWGlFbUY2M3J5RHQ0T3FVN0xnMTA0U2RzTjRoTTF4bUhrejFraUw3bitVZzNjV2ZTcUg3eS95SmV6LzFxTlR3VDltM21wV08xNjByR3N3VUlHa0E3OHdVWUVFMmlBTlNoSTUzaEtKb096RXFXS25MVWhIVmxZRWdXU0R0NUtROVY2Znk4KzZhMmhVSERBODBBMDRHcHA2OHRmTjdYOHFKcy9hRjhRVmM5SW82K0Y0OTZ5MnN3ZU9UOUZnNDlYTHhQREFtRGt6ZXR5eUdpR1N4SVRUMnBJaUQrQlJWdG1rdzk5eTBTZkVLVkk3QTV2S2RZVVpTZk1WdEo3Y3BPYXpsaWVHVXdSUzV3QXhHN0NnQ3l1SDZzNEJQYW1qRWdDN1B0TGg0VHpUOFdFR1RZayt2WnpaaXJkaDZIaG5vVzRiMUpFQ1dvcENiSHhwU3ozYlJmTmc2eitBZUFMb2hQTWhiL3Q2NXZFc29QUVMiLCJtYWMiOiJmZTdjMDg5YmY2ZDQ1NGY0NmU0MzE5ZmQyODIwZjhlNzhlOTE3YmE1ZDU0NWVmZGFmMzNlOTNiNTcwMWNhYmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1280728158 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtaV0VZbitFajYzMVVPak5TWHcvWkE9PSIsInZhbHVlIjoiS0RrdUhhVllKZ1BKb0M3U0JqamtTY0NVLzlCcHhndWh0RDVJYnY3Sit1SkxHRVZsTzdRdTdKRkZnM1Nad3M5bjZ2MGhEdDc4akJQQnJpVnpJRjFjM0NxQVcycEl0NHNGd0xUUGRKQTJMTVhkZi9GWjFrRlZKRUpxbHF5UHZyYm5MWjhON0NPaXJwUlVodU5zdlFrVmlScGNRSWsyRkdtY1diZG1jMUxHZ2ZDYlBlMXUyVmw2bVl3UTNuRGZ1WUVsNXdaWDV1MzhaMG9FWFVLWDJWUWlDNzROSW5BRjFlMVBka051YzNseE4yczVvN3lCSkMrdStPRllrUVdMa2R5Q3BWVHFYU3hIMFREczNrKytqS1BsUVJlQmhWVmh0RW9Md1hjODRoTGYwamJudUZqTkFKMFdaRDYvWEZ2OWVOcDNMbkw2R2hzMEJCbGIwR3FZRENIOHdxSXQ4V1JnQjN4dU1ZNHRWa0I4cUFPOWxrTXI3djJKN2Z2V1I4MVI4YTEyYXRUNGo4UFRGQ3RrUmJGT2NKb2dJL3pqMVJOZW5BamduT2JCZ01XblhEYTE5TXJKa1Z2VzFPVUhyMG1kWlJhQUtISjBxN3c0Q2Q4Mm1QNkJpcjFYeTVoeW9PQjR4aXVNQ3dnYjFSYVZ4UEdpRVhLTlE0WnBmejljTFJQaUJVS1oiLCJtYWMiOiIwMzFhNGJmOTdiODllYTk5OGFiNDc3ODM0ZGI3NmEzNTAxOWQwMzI2YzRkYmJmY2FhMWVlODU1NzA5YWZkMDgwIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFkYStWTzZGZ1FMYjBlNWM3dXZIZEE9PSIsInZhbHVlIjoicEp0VTM0Q1hWa1cyNmZJeG1PUmxGbXY1emoycE9zL0Z2OTExM1BOWFZ6UlIzczZQMkZYeXF3NlFQVFVvUlFpUVZMRUJzV28vOGk0K09zdjZZSmtWUkpYdG5Wd1IzMW90cHAzZDZYQ3Jyc1FaZjFKR2ZqMFA1RUdhQXFaeitJTVpKUXpPNFQrbVZxdVpMMWFscHZrdDlDRERlYzYyZWFySlpiR2xub3ZxUi9rVy9uUytocXdKNG52VWNoMDRqVC9QeDN3bnVDZXdvR09LR2JmY2hUL015cE50UStiWkRzcjVmblBBZm1Ra2pCbDRQaWxQWSt6ZGRBZDRvc0loWXNIR2xQSThBbUNwbytiZzlPWUNhM25pYmVTVDYyWC85Mkkxd3RCS0pySE9qMDQ3cnRjZko3WjQrbjUvSy9RdG4vcEEvU2FteGRuMHpSdUJWbzdHNkZqY2Jnb3M2VTVuWC9nb29nZ0w5bU1xamQ3T2dPdkFsUmw1Z0JPNDBLWUJkenpoWGhsU0VsRm1iVGVVNk1rNjZyR0xldGk2VVRhTUttMk9YYUN2VW12MlFpSXBPTlRmWU9lYksyNDdEcnZhWnVpRnY5NjYyOEZuUXAxbXZDYUx0WExTeWZSRjA3WFFsMlBVME1SbHQ0dWdWa1dFOWJoc3RKbmU5SVM5eFRKZ2c4bVgiLCJtYWMiOiI0NDBmNDIwMjIzNGM3YzM2N2QyY2YzNWU3Y2I4YWI3NDE2MjdkMDBhNjM4Y2M5MDViM2RkMGQxMjNkN2VhZmZjIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtaV0VZbitFajYzMVVPak5TWHcvWkE9PSIsInZhbHVlIjoiS0RrdUhhVllKZ1BKb0M3U0JqamtTY0NVLzlCcHhndWh0RDVJYnY3Sit1SkxHRVZsTzdRdTdKRkZnM1Nad3M5bjZ2MGhEdDc4akJQQnJpVnpJRjFjM0NxQVcycEl0NHNGd0xUUGRKQTJMTVhkZi9GWjFrRlZKRUpxbHF5UHZyYm5MWjhON0NPaXJwUlVodU5zdlFrVmlScGNRSWsyRkdtY1diZG1jMUxHZ2ZDYlBlMXUyVmw2bVl3UTNuRGZ1WUVsNXdaWDV1MzhaMG9FWFVLWDJWUWlDNzROSW5BRjFlMVBka051YzNseE4yczVvN3lCSkMrdStPRllrUVdMa2R5Q3BWVHFYU3hIMFREczNrKytqS1BsUVJlQmhWVmh0RW9Md1hjODRoTGYwamJudUZqTkFKMFdaRDYvWEZ2OWVOcDNMbkw2R2hzMEJCbGIwR3FZRENIOHdxSXQ4V1JnQjN4dU1ZNHRWa0I4cUFPOWxrTXI3djJKN2Z2V1I4MVI4YTEyYXRUNGo4UFRGQ3RrUmJGT2NKb2dJL3pqMVJOZW5BamduT2JCZ01XblhEYTE5TXJKa1Z2VzFPVUhyMG1kWlJhQUtISjBxN3c0Q2Q4Mm1QNkJpcjFYeTVoeW9PQjR4aXVNQ3dnYjFSYVZ4UEdpRVhLTlE0WnBmejljTFJQaUJVS1oiLCJtYWMiOiIwMzFhNGJmOTdiODllYTk5OGFiNDc3ODM0ZGI3NmEzNTAxOWQwMzI2YzRkYmJmY2FhMWVlODU1NzA5YWZkMDgwIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFkYStWTzZGZ1FMYjBlNWM3dXZIZEE9PSIsInZhbHVlIjoicEp0VTM0Q1hWa1cyNmZJeG1PUmxGbXY1emoycE9zL0Z2OTExM1BOWFZ6UlIzczZQMkZYeXF3NlFQVFVvUlFpUVZMRUJzV28vOGk0K09zdjZZSmtWUkpYdG5Wd1IzMW90cHAzZDZYQ3Jyc1FaZjFKR2ZqMFA1RUdhQXFaeitJTVpKUXpPNFQrbVZxdVpMMWFscHZrdDlDRERlYzYyZWFySlpiR2xub3ZxUi9rVy9uUytocXdKNG52VWNoMDRqVC9QeDN3bnVDZXdvR09LR2JmY2hUL015cE50UStiWkRzcjVmblBBZm1Ra2pCbDRQaWxQWSt6ZGRBZDRvc0loWXNIR2xQSThBbUNwbytiZzlPWUNhM25pYmVTVDYyWC85Mkkxd3RCS0pySE9qMDQ3cnRjZko3WjQrbjUvSy9RdG4vcEEvU2FteGRuMHpSdUJWbzdHNkZqY2Jnb3M2VTVuWC9nb29nZ0w5bU1xamQ3T2dPdkFsUmw1Z0JPNDBLWUJkenpoWGhsU0VsRm1iVGVVNk1rNjZyR0xldGk2VVRhTUttMk9YYUN2VW12MlFpSXBPTlRmWU9lYksyNDdEcnZhWnVpRnY5NjYyOEZuUXAxbXZDYUx0WExTeWZSRjA3WFFsMlBVME1SbHQ0dWdWa1dFOWJoc3RKbmU5SVM5eFRKZ2c4bVgiLCJtYWMiOiI0NDBmNDIwMjIzNGM3YzM2N2QyY2YzNWU3Y2I4YWI3NDE2MjdkMDBhNjM4Y2M5MDViM2RkMGQxMjNkN2VhZmZjIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280728158\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}