{"__meta": {"id": "X26be9f430bfd6302ff409217629ec0fb", "datetime": "2025-07-12 16:36:00", "utime": **********.728124, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.253168, "end": **********.728147, "duration": 0.4749789237976074, "duration_str": "475ms", "measures": [{"label": "Booting", "start": **********.253168, "relative_start": 0, "end": **********.653876, "relative_end": **********.653876, "duration": 0.4007079601287842, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.653893, "relative_start": 0.4007248878479004, "end": **********.72815, "relative_end": 2.86102294921875e-06, "duration": 0.07425689697265625, "duration_str": "74.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45708040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01842, "accumulated_duration_str": "18.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.681615, "duration": 0.01754, "duration_str": "17.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.223}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.708141, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.223, "width_percent": 2.443}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.71681, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.666, "width_percent": 2.334}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1400838894 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338115835%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZhTk1EbXBCZWxvQUR5Y2FmQkxodXc9PSIsInZhbHVlIjoiVjBEb1dIVGJqNU5uVGEzeHBDSHNrVkF4TlY0eWh6UlpQNkF1dDhBOC80TVp1VXF0M1JXRDN5YVlYWUtNWTFqTTk5eVMzZlRyUHRYaDBDTVQydTVxTmNrSWt1ZTBSNzg1UFl1VDhaQTlQTHQ3amVUOTd6UklmOTE3SVo5NjFWOUdLUmQweEhrdjBLcTU3NTRkaUc4djMvbE9QVFpyUjRTUlVCSTFYL01EVm5Ic3ZqamZFWWxCVmE5QytwZVdvWTdDYnB1a2owRStaYi9GSjdoeU52SFhsSUJWSkFYenlUdCtJUHVFbzBhQzYxMDNaakxLR3FMS1hmbU5sOCsvQVc4WjFsck94eVR6RFlaYmF4ZnFIMWgyTnpxMUlqelZLc2ZiVHJ0a2NEN1VKREZmNmRaTHVZMURRUm14c00rckxyTnZUdVNhbjdjYmQzemlaTkJueHR4SEVwaEVGUkFPM0ZySkNndVdiZDEya2lNRFIyWjh0N3FvSUFZT2RtTklDS1pjY2FocndiUDVXUWM1aWUrNWVGMHhHd2VuOUp0dzhiZUQ1RFgzVEUyMzl1anNBaWlCSnYvaG5nU3FDVWJuZWdDL0lEV2VhL2U3ZnJWV0hNbFZWVlk2SWkzdm52SlpURHYvZThUU2UyOWNIYUQ1Z2hxamxhaE5XREhqZS9GTUdGaHUiLCJtYWMiOiI1OTY3NDM4NmFmN2NlYjc3MjE5NGNmYzFjN2M5NTNmM2M0OGQ0MTQwN2Y4ZTdhOTYxN2Y0YmQ3MmZjODMwNmZjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdydUlFb0lOV0dWdlZ5V3ZYWHN0MWc9PSIsInZhbHVlIjoiZ0tsRHJnRUZUZUcyYzRLbmZBaFpLYXA3azZweUU4UEZVQTJ3YXdCY3RRZ0JVSEtvK3pnZCtWaVRUbXFyK3B1T1FWdW1OdUpET0lSbSs5dFp4WFZrVUxrU1dIWjBDOUVxZlJrSTRlNGNqNHErVFMzQUQybUVFYmh4WVIwY2hPQ2tVVUVKQWRkMzhpNzhYUWU4TjdtSVBaWGlFbUY2M3J5RHQ0T3FVN0xnMTA0U2RzTjRoTTF4bUhrejFraUw3bitVZzNjV2ZTcUg3eS95SmV6LzFxTlR3VDltM21wV08xNjByR3N3VUlHa0E3OHdVWUVFMmlBTlNoSTUzaEtKb096RXFXS25MVWhIVmxZRWdXU0R0NUtROVY2Znk4KzZhMmhVSERBODBBMDRHcHA2OHRmTjdYOHFKcy9hRjhRVmM5SW82K0Y0OTZ5MnN3ZU9UOUZnNDlYTHhQREFtRGt6ZXR5eUdpR1N4SVRUMnBJaUQrQlJWdG1rdzk5eTBTZkVLVkk3QTV2S2RZVVpTZk1WdEo3Y3BPYXpsaWVHVXdSUzV3QXhHN0NnQ3l1SDZzNEJQYW1qRWdDN1B0TGg0VHpUOFdFR1RZayt2WnpaaXJkaDZIaG5vVzRiMUpFQ1dvcENiSHhwU3ozYlJmTmc2eitBZUFMb2hQTWhiL3Q2NXZFc29QUVMiLCJtYWMiOiJmZTdjMDg5YmY2ZDQ1NGY0NmU0MzE5ZmQyODIwZjhlNzhlOTE3YmE1ZDU0NWVmZGFmMzNlOTNiNTcwMWNhYmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400838894\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1593731957 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593731957\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2008387057 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9COUdwcUI3WFB6UWl6MFpoTmFxS3c9PSIsInZhbHVlIjoiLzdqYWs2YlZIRkx5T3EyaG0wam5vUzdybm1xd2pveWh1cnlvQ3UxalRmSk91R2c0aXRXVlgwZUJpcUJSb1MwbEtPMDhldnkva3N6NFdYZjhyYmRmWUxrSUtqa0ovTkFuSHp4TkJjVmdOOWVySDBtU1dUVjJkYVdXdVg5U0Uzanl0SmtySDJLdlp1MG1paG1QblA0L0ZIM1VjdkpxMS80Z1JuVkgwMHdwQVlOR05DMUR4V0xFcUQ0Wm9yY1BUYVVBVHVJWXhLeTF4c2FCOCt3S1FVQUYwNDIzTGR1UElVMTY4ZWg4THNITCtudnpvUytNZlJqNTVZRVc0aVUyMjVjYzhtK2dxRE0zUVpVYTZhM2xMeDhUQXlhODA4NWhVM2NrM1JPRzNhSXlJUjJuNUpEOEVEK2tMTkFDbEpjTkhGM0hKd005bUd2MFJrMmU0ZGRmOXB4VmVoaUMxb25nT1I5TVl4QkRsTDdrd1R6TkZRTFI5SWNpeHF0YXRiNmUyUkFFemsxREQ5ZHJMV0RvcnJFaTA3TXFWZnlURWdEc1JUdUs2QTlWcHE2alNaZ29xRVhzaVdiMlhyQUIxS3pOaGVnM25kOWtndWhUajBJS0lhL2ZQMytnZExCb0l1NmVISjJ1NW5lRzB1T2N3NEZFU2o2T1NXaUNnWUgzSitMODFucFIiLCJtYWMiOiJhZWIwOTg2ZTZmMGE0MjE2YzU5ZDY5MmRjNGYwYWI1YmM4M2ZhODlhMjUwM2JlYmExN2E0N2ZkMDg5NTc4MTY2IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik52WXMwNzVUYUNpMElGMnFVVFloRXc9PSIsInZhbHVlIjoiSEgvcFBvN2JqRzl6WG4xRHROTThHT25QeEo0L1I2MDVLSEJHT2s2dkVjNTlFZThGVnpoa1kxTE5MTkhiL09IenI3ejlLcHpSQWROK295M3BIMElKTHp1ajhPd0ZYMEZtVklNeDdmS2dWaUlXdi8vbHlFbGVVMXVIMVQ0NlJTNzE5RWtoU2VjUFYwdGxmYlhHWW5kYVZheG44WnlEbkZNc3d5UVZtUGdwU2tLc0xLM3ZTTnRWM1pMNlArOWFXRnJ1TUVHUUZzYXF2UlBoZjlROTdVOEZJNEMrbGh6dHQyM1oySmVIR2FMOUxkWWJVYUE1M3dRQWxVRmxWWkU1UEtDNU5QbDk3OGlFM2xUaEF4bWVldnVrQ3BWYlhUamNtYXlYNlJ3TVNJN2VXY1FJbWMvUTMxU1kyOEwrNGN4UUNzMXNEWjJ3L1hMUmluRlgwanp3KzNDREhPMkc4S2xGOWVkN0xIcmgrK1oxYW82dHdxL2tIbUoyNlZZZjdRcCtGV29Fa1ovT3ByMXRHMkhxS2FLWjZVWlF0ODdrK1NZYmMwNHROWkwyZTlpV0pOTnpDZ0szUkRXUS93bG1GWE1PYmdEcmNoYWtEakd4am15U1dITEVnOWE2dlFFSG5EeUgvTlJQNTRLTHllME5xQWhrTTBSTFVTeWd0dytITXM0QlNlUUEiLCJtYWMiOiJmNzc0OGE0ZmIzYjNjZDVmNGE3NTRhNzhkNWY1NGIyNDgwMmY3Y2I1MTAxNjlhYzkwYzM4YTJlMzZhZTc2ZjUxIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9COUdwcUI3WFB6UWl6MFpoTmFxS3c9PSIsInZhbHVlIjoiLzdqYWs2YlZIRkx5T3EyaG0wam5vUzdybm1xd2pveWh1cnlvQ3UxalRmSk91R2c0aXRXVlgwZUJpcUJSb1MwbEtPMDhldnkva3N6NFdYZjhyYmRmWUxrSUtqa0ovTkFuSHp4TkJjVmdOOWVySDBtU1dUVjJkYVdXdVg5U0Uzanl0SmtySDJLdlp1MG1paG1QblA0L0ZIM1VjdkpxMS80Z1JuVkgwMHdwQVlOR05DMUR4V0xFcUQ0Wm9yY1BUYVVBVHVJWXhLeTF4c2FCOCt3S1FVQUYwNDIzTGR1UElVMTY4ZWg4THNITCtudnpvUytNZlJqNTVZRVc0aVUyMjVjYzhtK2dxRE0zUVpVYTZhM2xMeDhUQXlhODA4NWhVM2NrM1JPRzNhSXlJUjJuNUpEOEVEK2tMTkFDbEpjTkhGM0hKd005bUd2MFJrMmU0ZGRmOXB4VmVoaUMxb25nT1I5TVl4QkRsTDdrd1R6TkZRTFI5SWNpeHF0YXRiNmUyUkFFemsxREQ5ZHJMV0RvcnJFaTA3TXFWZnlURWdEc1JUdUs2QTlWcHE2alNaZ29xRVhzaVdiMlhyQUIxS3pOaGVnM25kOWtndWhUajBJS0lhL2ZQMytnZExCb0l1NmVISjJ1NW5lRzB1T2N3NEZFU2o2T1NXaUNnWUgzSitMODFucFIiLCJtYWMiOiJhZWIwOTg2ZTZmMGE0MjE2YzU5ZDY5MmRjNGYwYWI1YmM4M2ZhODlhMjUwM2JlYmExN2E0N2ZkMDg5NTc4MTY2IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik52WXMwNzVUYUNpMElGMnFVVFloRXc9PSIsInZhbHVlIjoiSEgvcFBvN2JqRzl6WG4xRHROTThHT25QeEo0L1I2MDVLSEJHT2s2dkVjNTlFZThGVnpoa1kxTE5MTkhiL09IenI3ejlLcHpSQWROK295M3BIMElKTHp1ajhPd0ZYMEZtVklNeDdmS2dWaUlXdi8vbHlFbGVVMXVIMVQ0NlJTNzE5RWtoU2VjUFYwdGxmYlhHWW5kYVZheG44WnlEbkZNc3d5UVZtUGdwU2tLc0xLM3ZTTnRWM1pMNlArOWFXRnJ1TUVHUUZzYXF2UlBoZjlROTdVOEZJNEMrbGh6dHQyM1oySmVIR2FMOUxkWWJVYUE1M3dRQWxVRmxWWkU1UEtDNU5QbDk3OGlFM2xUaEF4bWVldnVrQ3BWYlhUamNtYXlYNlJ3TVNJN2VXY1FJbWMvUTMxU1kyOEwrNGN4UUNzMXNEWjJ3L1hMUmluRlgwanp3KzNDREhPMkc4S2xGOWVkN0xIcmgrK1oxYW82dHdxL2tIbUoyNlZZZjdRcCtGV29Fa1ovT3ByMXRHMkhxS2FLWjZVWlF0ODdrK1NZYmMwNHROWkwyZTlpV0pOTnpDZ0szUkRXUS93bG1GWE1PYmdEcmNoYWtEakd4am15U1dITEVnOWE2dlFFSG5EeUgvTlJQNTRLTHllME5xQWhrTTBSTFVTeWd0dytITXM0QlNlUUEiLCJtYWMiOiJmNzc0OGE0ZmIzYjNjZDVmNGE3NTRhNzhkNWY1NGIyNDgwMmY3Y2I1MTAxNjlhYzkwYzM4YTJlMzZhZTc2ZjUxIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008387057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}