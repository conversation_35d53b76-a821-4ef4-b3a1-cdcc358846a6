{"__meta": {"id": "Xc177adf8cb52538e69d537797f928c00", "datetime": "2025-07-12 16:36:13", "utime": **********.321951, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338172.847486, "end": **********.321967, "duration": 0.47448086738586426, "duration_str": "474ms", "measures": [{"label": "Booting", "start": 1752338172.847486, "relative_start": 0, "end": **********.248226, "relative_end": **********.248226, "duration": 0.4007399082183838, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.248235, "relative_start": 0.40074896812438965, "end": **********.321968, "relative_end": 1.1920928955078125e-06, "duration": 0.07373309135437012, "duration_str": "73.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45708016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01759, "accumulated_duration_str": "17.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.278709, "duration": 0.01657, "duration_str": "16.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.201}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3047202, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.201, "width_percent": 2.786}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.311871, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.987, "width_percent": 3.013}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-994081580 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-994081580\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-682225126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-682225126\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1413193916 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413193916\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338164909%7C19%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilc5TFBqUFZUYVRXMkhjNy9DMzluYXc9PSIsInZhbHVlIjoiVzI4TWtzQzB0OE50MzRZUmxhQ203dHd4NHVxM3Z4akpwWW9uVWEvWGRWNGZhejdXNFdTN0E2VGhiZDZGV0JUc21XUDAzeFJvNzRZTVZxbVNFbTJmK1ZFTzN2TXRVNmNaRVVlcDBSTUVrTWsybjBHWGVtQjN1cllnZDhVZEdwZnJ0c1BoamtLa1lsNVNvRGpBTW5wS3VBYWRUNG1zQSsvMUNQcW5idGVyTnY1aHdkRDNIUEJLc2JtU0g0YzNmTHpBM0J0K3VtUU1RYmpKNzBvcFJSUHhadU1tOFVMZ091NS8xNGExMEVsUGZ1N0FobDlPNnF5dXh2V3FuR2JHTDBjQUtRV3l5THQ2VVIyMElTa29TOVVLcHU4YmhFNlptcFhHUzlJNCtBdVByUkNBUVhZOTNlTTE4emU5dU0wSktQL3FCZEM5bk93eThYSk52U0cxOHB1a1Q2ZnZqa1lQQ01aTWozS041ZVpXU282OG5QY1pxMmI5SFdDcDJJdHhMVVhhNCt5UUtDTElMSk14aUFtZHM1TkYyZ0NvUW01WEZTaVl4NWRtVEowREhGUC9NVUdKM3VZWmI4OGl0bnhPYkxlbEx0eVlBanRhd0Rrd2w5MjVvbVlkVXArbEx0UllQQU9FUHlhTTNqN0swaTJtdXo5NkZXaSs5eFlFTWJTQ0lhRSsiLCJtYWMiOiJlMDQ5YWExMjY3MDM5MTE3ZTc5NGVkZDcwYTVhZjg2OGI0N2NhOTMzZmQxNDNlYjNiZWU0YWIwMThiNzNkYzgyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik8yZGlSdXFjWnNidDJQa0JKZnVpdnc9PSIsInZhbHVlIjoiRGJaMXowemI5eDRZL2x3OSs3UlZLUDdXZ1dHNzhKeWJzTXhrWmxJVVZZVitpeU9FTlFOcXc2cTEwRnhsYnQvK3NPREl3Rk9GdnVnMFhVZDBUR1JtZnBac3JSV3I4bFlidlYzOWZDYVBqbCtCcmVINWtoY1NtaWpUN1JRamJJeEpyMU04TWV5SUhjQU4rOUNKUkU2ZWhEVXd4L1FlM0IvUFhJUFgrajNwNVV2M3UzQmwvQ04vSmNkbGNTSml3Si9KVHNLZXR2V0llNk5iNWlKRHBmb2pOQWVDamM4OWN4QnZtK2RDdktiMWxTVFlIeEMrc0FKbVZOK0RWQlN5VUc1cUlBUGtGR3ZEeTFyeUY0SFEyV3lBblVZU2YwaUxZWE1GTlhpck5DWFg4WlZZcXdvV0lNZi95QUhEVUtHb0p2TWJOcm1xc0REUURaSm1UbVNoYWMyMmtXVWxQbmNLNjBNRDlaRzBlN0UrdzdVY3huZDh0bFNGNW1mZVlTWjdMMjZHcFFTYTBTWnFNUkZXS3J6YXh4akdzY3A4ZXFxc0RuMDd4MVp1a2I0WE5ycEN0OGRUdG5hV0MvNXRVK3Erd0t0VDlaUFRRaWJaMUlWQnRYM25WRWZUZkREWXZZQUU4QlY4QUpWbkJwOUtHYmFOclFJTXg3NjNaamVvdEx5bStONDgiLCJtYWMiOiIwZjk5MGU5YTBiYmEyMGRmMmU2YjgzMmMwMzRkM2EwY2VhYWYwMDBiNDZhMjJkMmM4MDU0YTI2NmExYmJjYWM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-549061479 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRESVVTemZ4eVp2NE4yK2JnV1NMWUE9PSIsInZhbHVlIjoiQ0xqTml0bVhCcmora282dytSRC9JWDZvVUxBNmhZa3ltWTEzZ1Z6UlJpb1BlYlRpOFpPcWtObDdlaVBEM1lQL2o1TWlxOS9Qd0tKbmRpWGc1Qkpzdm5GSm11OU9vb2hPL29OTUJsRWl2YVVmRHA2Lzc1WTlvQmpZeDFxOTJZT0F6ZnU1cW5IQkJjQitlalBTK3ptclByVEQyaVUyK2cxSGxrT0c3Vi9hbWZoREZ0ekJOR0gvaWZpTSt2VThuNEROWFJuOFRsMk1XWkM1RzNSYlNlTitjUFIzYlk2Qm9aNlcrWFVjQW04dW92eThOeUVsVG84NnJMWlpIaEpTOTlRelJUdm5pVmIrbW5DOWpKT0NaZFF5VTJ0azB6WDczdFFxTGVSRVZPblZOeDA0cnlnM2oxZ1pzMVBkTzdPbkxRbng1YTVFTEJjUlMyMTdpcGZXdldMQXZsakU5dDRoZ2N1RHUyZ29FZG9KZm13cmpuNTB6TnVPYzVkUGpZQXJGaDRZZThrcXkvWmtQWThuVmdtdTE0cWVNcjM1Nk1XTGl3aVdPUEovTTdxb3ArUk9nYWlHQ2ZXaHhmQ2xGYlUzMmRBZkpndkV6S1Q1N2hDc1ljY2F4UWx3dDRuOFA5L2psYXlOYkljbEJKbFJNbUpnSW5ZcXZMbGhpU2p2RnBIYlJ2WGEiLCJtYWMiOiIxMDg0MDIxYTRiZDA5OGRlNTNlMjM3Mzg3Y2MzMzViMzNmYjdhMWU4ZDFiZGFmZTE1MDA3Yzg1YzlhYzhmOTU5IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZZTmpuaG5uTVpDejQvcWxzRnIxNlE9PSIsInZhbHVlIjoiWTBFN1BidGszbUdrSGt3Q05DMFR2T1dvZU9sMkxJWlZ3YVZZb1RZTWt1TjhjQm5Na0xYR29HUm8wMHVBN0ZDRkRPTUh0aE5FN054enRmeUNWbHlSdmg3QnRRdXYwU2lIUEhhd0xTYTBYZmcrMGx0VGhtNlN0S3hOWnBZcjZNclFFdGJxTFpuR0hjQWt1RUFHdGgybVpvQmNBTzRqU3VjSjdoVDM2N0h6TGtEeGM4Qm5vcWtJY0xmMlNwUkZoYU1XdlpSN0ZzV3N6UGpjYkhFYU9ZWG01TStkdG50bHkzWjR1WTVnRVE3WVFJRi9raGhzOUM2ekpDTWd6TGdPWVA5L1NFV1JXUzRkeGJEMXI1OHZLeC9TZE9LaG51QUh3SEljcUtaY3VIbUIreDRiY2Z3aDlGOHp5NmkwUkFzeWljZmdlTXF4eTBJQ1RxU0J5SVhUdkhYUEVMdVg1WThEZWtCMTVmVUlwdmFSVE40SE9hdzF0VHNRTzJ1ZWlYYXc3TWRjRndweXg3R1ArckIyOTNwVkY2S0hLYStIK1NLcTVGU3FzMkNFQStyb1lGWjg5K2g5elIwS09aVThDSVFpTkllUEZGdmFFZ0YydE44WFhOSVBFd2s5Zmd1ZFV3SjUzaFlGUXpmWUU5aEl0MTVnbWlHRGFOM3JheG1va3QzNTk4NU4iLCJtYWMiOiI0NjE5NWI5OWQ1MzYwMDQyZTVjMzIwM2IyNWQ0YTA1YzVkMTc4M2RiYjA1OWM4YWEzM2MyMTI0MmExYTkzNmVkIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRESVVTemZ4eVp2NE4yK2JnV1NMWUE9PSIsInZhbHVlIjoiQ0xqTml0bVhCcmora282dytSRC9JWDZvVUxBNmhZa3ltWTEzZ1Z6UlJpb1BlYlRpOFpPcWtObDdlaVBEM1lQL2o1TWlxOS9Qd0tKbmRpWGc1Qkpzdm5GSm11OU9vb2hPL29OTUJsRWl2YVVmRHA2Lzc1WTlvQmpZeDFxOTJZT0F6ZnU1cW5IQkJjQitlalBTK3ptclByVEQyaVUyK2cxSGxrT0c3Vi9hbWZoREZ0ekJOR0gvaWZpTSt2VThuNEROWFJuOFRsMk1XWkM1RzNSYlNlTitjUFIzYlk2Qm9aNlcrWFVjQW04dW92eThOeUVsVG84NnJMWlpIaEpTOTlRelJUdm5pVmIrbW5DOWpKT0NaZFF5VTJ0azB6WDczdFFxTGVSRVZPblZOeDA0cnlnM2oxZ1pzMVBkTzdPbkxRbng1YTVFTEJjUlMyMTdpcGZXdldMQXZsakU5dDRoZ2N1RHUyZ29FZG9KZm13cmpuNTB6TnVPYzVkUGpZQXJGaDRZZThrcXkvWmtQWThuVmdtdTE0cWVNcjM1Nk1XTGl3aVdPUEovTTdxb3ArUk9nYWlHQ2ZXaHhmQ2xGYlUzMmRBZkpndkV6S1Q1N2hDc1ljY2F4UWx3dDRuOFA5L2psYXlOYkljbEJKbFJNbUpnSW5ZcXZMbGhpU2p2RnBIYlJ2WGEiLCJtYWMiOiIxMDg0MDIxYTRiZDA5OGRlNTNlMjM3Mzg3Y2MzMzViMzNmYjdhMWU4ZDFiZGFmZTE1MDA3Yzg1YzlhYzhmOTU5IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZZTmpuaG5uTVpDejQvcWxzRnIxNlE9PSIsInZhbHVlIjoiWTBFN1BidGszbUdrSGt3Q05DMFR2T1dvZU9sMkxJWlZ3YVZZb1RZTWt1TjhjQm5Na0xYR29HUm8wMHVBN0ZDRkRPTUh0aE5FN054enRmeUNWbHlSdmg3QnRRdXYwU2lIUEhhd0xTYTBYZmcrMGx0VGhtNlN0S3hOWnBZcjZNclFFdGJxTFpuR0hjQWt1RUFHdGgybVpvQmNBTzRqU3VjSjdoVDM2N0h6TGtEeGM4Qm5vcWtJY0xmMlNwUkZoYU1XdlpSN0ZzV3N6UGpjYkhFYU9ZWG01TStkdG50bHkzWjR1WTVnRVE3WVFJRi9raGhzOUM2ekpDTWd6TGdPWVA5L1NFV1JXUzRkeGJEMXI1OHZLeC9TZE9LaG51QUh3SEljcUtaY3VIbUIreDRiY2Z3aDlGOHp5NmkwUkFzeWljZmdlTXF4eTBJQ1RxU0J5SVhUdkhYUEVMdVg1WThEZWtCMTVmVUlwdmFSVE40SE9hdzF0VHNRTzJ1ZWlYYXc3TWRjRndweXg3R1ArckIyOTNwVkY2S0hLYStIK1NLcTVGU3FzMkNFQStyb1lGWjg5K2g5elIwS09aVThDSVFpTkllUEZGdmFFZ0YydE44WFhOSVBFd2s5Zmd1ZFV3SjUzaFlGUXpmWUU5aEl0MTVnbWlHRGFOM3JheG1va3QzNTk4NU4iLCJtYWMiOiI0NjE5NWI5OWQ1MzYwMDQyZTVjMzIwM2IyNWQ0YTA1YzVkMTc4M2RiYjA1OWM4YWEzM2MyMTI0MmExYTkzNmVkIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549061479\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1981844261 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981844261\", {\"maxDepth\":0})</script>\n"}}