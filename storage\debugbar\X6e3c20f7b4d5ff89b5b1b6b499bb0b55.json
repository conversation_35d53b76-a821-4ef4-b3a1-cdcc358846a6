{"__meta": {"id": "X6e3c20f7b4d5ff89b5b1b6b499bb0b55", "datetime": "2025-07-12 16:21:51", "utime": **********.656448, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.179743, "end": **********.65647, "duration": 0.4767270088195801, "duration_str": "477ms", "measures": [{"label": "Booting", "start": **********.179743, "relative_start": 0, "end": **********.564758, "relative_end": **********.564758, "duration": 0.38501501083374023, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.564768, "relative_start": 0.3850250244140625, "end": **********.656472, "relative_end": 1.9073486328125e-06, "duration": 0.09170389175415039, "duration_str": "91.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45710200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02682, "accumulated_duration_str": "26.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.597632, "duration": 0.02582, "duration_str": "25.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.271}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.632958, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.271, "width_percent": 2.088}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6397922, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.359, "width_percent": 1.641}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1699252283 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1699252283\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-761782520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-761782520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-860554783 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860554783\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1607510359 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1946 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752337205003%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImIrRVRwKzIzVUhUMmh5MlJTRWk0NlE9PSIsInZhbHVlIjoiZnd2bFNYRG4wc0RBYUxxU3FVSUJhNnJ5d0FnOSswbEtlUndjMjBSd0VlUmsvam54REJZRTJ6aSsxWGZaTVdKWlAyWTZabjVTTUFqRXdPSUsvZks5R2FUNTFjbnI2ekYvOUJ2WHpmYzk2Y1BlcjdrSzMxaG9aRFMrbWNaOXI1SUNNYXJsT1g3S3R2UTlNNEFXZWwzekFXaHJwQ2QwZHhydUo1ZEROZ3NiNGdkUzAycVlOUFBYRGh2cEoyZndMTXFnQ2ZWN3VwbTBpUnFkTjBGT3cwVWRob2NobXBUVFEzbitGNWljSDZBWWk2QTV5NmYyU1VmL3pDb1JlYXpHQTlQSnFXUnBsdzdrcEw1N25DMTFnczBRK1dvT0dBYUt2UWlHSENXWTUwWFhPVDNmb1czS2FGUkVyNHZNVjM3OXIrOEEzSHdaOGU1M3R5MlU5K3FHbnhiUzNjRHdvSjlkNCtsZFJuaWFSUXNUaDREdjF4QWFpb1NielJuVVd1anVzZ3pmcS9RZmpmNU9IUTBsd0pvc24rNVpUN1R0RVN6TXEyaTd0YUlDdEZTZVIzUnBzYldTTlBzY3F1ZjNsSE44TlJPVnEwSE9xYnB3Mko5WkdjQWFaVWVDZ2FLMjBua2lKL1R3UUYxWlFjRHMvTjVkUUhBYzdJMjlDbFVRNExCeE5uOWMiLCJtYWMiOiIzMmI1Yjg3MWUzZjU3NzkyZTViMWQ2N2QxMDQwOTQ2YTM2NzkxNmNjYzMxN2QxM2EyMzcwOTNmNTMzOTZlOGRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZZZGJNckc0eFpBSXdSSmdheDI1dkE9PSIsInZhbHVlIjoidWl2RHlsMEJGT2F2STBQYlVzdzRERXBBaXdZU1h1aGlzU1JVQ1hmb1BaTWxBMEg2czVRT1lDdi9lZnJpZTBkVjlKSmdXckViQ25VM2Q5MEs2MUg4MExrby8va0dKTHQyM1BiWW9vLzhDczBhbnk4dTJ4MFAvK004dnFjOTMzc2ZHKzhFWnJkUmR4NDRxYVVNZmFEaGd1VWNRMVRpZnJFdlo3NURwV0dVcHBLeGJQbFE4NXRPWlU5NVVnQSswcW5tV3IyTk9mNjlqQmY0OVNzeERNb0pMNFU4YVdkMjA0NVFNd2RuMnhBKy9KekhZSnQ0ZkRkQWxRSXd2cVRkZE1ZazZ1N1R4Z1ZkeTlZdmtHZnl4SnFZQU9SR3E4M1gvcUFvOWVPRWFjS1A4UGxZV2FGekJmTWJZWWRXOTNTN0hTekxReEdrM2N2RmRIb09EaDVDYnJoekhMazZvZERpRmlMNVByV3FsZGQ5Vnlma1NNeHBiNnRpRFpWdkV5eWRxUkR0aUJidDhnRUhpOXNkTnhYbytrSHdSM3ZaOWVWbS90dC81a3VqRlZobklqdWpCWXFOLzBzNWx3RmlBeGllQ1VsbmNYNVFyTlkwMzlxZFR4TXdCSEUwSVIzcmlTaS9HMWhla0NnVU9sakFNeWVERFFLbDg5N0JsMVhyYmF0cWp3RGYiLCJtYWMiOiJhNjM1Mzk3ZTExZTMxMDQwOGI5ZTNjYTkxYTY3YjkzODhmZWQ1NDFiYmI0NGU3OTRkMzVmMzEyODNhODc3MzNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607510359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1163908887 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163908887\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-894153296 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:21:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikd1RHRFMmQ2RVU4MkxEdlEvd1Z5NWc9PSIsInZhbHVlIjoicjdMNjNCNXBvU3IvY0tmTk9WTEZ3UlN4VHUvM0F2cWxNZ0QrQVduT0VoTzcvWitKcU1LMCtUOFBZNyszTVB1MHFweWdHbnNhQUJ2cy80YTYwR1YzQTlocGFTQ1RFWWVSNE4rb3EyN0Z2T015LzBBZGtjYStFWGE2QXFZRjdaK0Q2V0gxQU9zSGlzWDB6V2JKNDBHemtHY08xang1NVFYMzNaWmZ3SDJjZDExOUNsN0JGTUZiWHFNQlBiVGtaRnh1bDlOeVJkb0RhcmZsWkt3QUFyREtpays4ZEdhVGhXZVBaNlVyK21QSXFwRGgzMklYSmNWUndDOUhEZVlUazVld1VYV3dUUnE2MVhMakc5RE1XcUJIZUxKdmYzYkx3eTdTT1J1UVlJOUg1MjNTTDlQcjFxdHRjREZucG9Kb0I3akdsWUc5TE4yVmZwWHNlaFUwTkpVV0RyWWJ4WnJWdGIxdjd3OWwzMGJiM0xVRGtSVnhGVWJuK1FQU2tycGd5M2hzSkhGaEpidmNqdGMvYWNGdFRmT3pObzdVSjlnM20zUmtPRjZVT0NxMkVIYmZWMmx0S3dQbmJKWTdyMVFvbW81S21scHM0Szd5anU3M0pORGdMU25VTVZFRHJpMk5STjFYMlRUbFIza3Z1U2hMWjk0cE02NTFwQm1BTWpjR1lxZG0iLCJtYWMiOiIxOTM5M2M3OTc3MmZlZDYwMTc5ZGMxOTYwOGU4MmU4MTE0NmE2ZWU0YzMxZDIzOTYxNTk4YTA0NGY2MDNhNWE0IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:21:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IksxU0RMWTNncnJ0UzhpdFF1YURWVnc9PSIsInZhbHVlIjoiSUR2eWZCcUd3N3lCZU1lZlBIaFBMYUtOV1NxaFVLQ1ZDNExHSFVsRVlxS0lKWFd3Y29TVC9hTTZwNjZCdUdqOEFHUDFqcXM4NGlVTGMrLy90dURrQzJGZy9RN0hQWVZMVXo0REE2NkljdHpsOGdUZ2tqQzBVQ1pEWXNETnRoNURzYmJPV2lXUHQ4TEJySmViam9IUzVtc1NxRlJrVWdUTDFyVjZrR2U4M2NFdjB5V0doOS9SVnkwbDBOWTc0c2xTdklYZ21aU1lMTk5NWHpsd0d2aXJxMk4waVVUVGJyN21KcUNUQU04VDZwaXVMZ1pobTkxcVNFcjVaZWN3K2lhaEtGSlZTQUQ1OUJlOGRqTXBrWVJGWDdzZG80UTd4Nkp1VEFqcDFmd3RxSjRDQWF6MlRhNjdXYmdVWXlENXEwQzZTeUhFUVFnSVM0b2VUT0dNY3VmQnM5TlFuQXJrK1dxbmtRbm5GVlpNNVFQTFVvaGQvbm1UbFd2K2xIbTIvcXNPbWVzNWVvay9LR2E1azJhb2IvT09qREFZcDhUVzB4dkU0TGhxWGJrV0lHRXJhOGZEUEloWXpVVHRnQ25Idmw3MHZXdUFUTjltbFVmY0hIY0JjQTJGZm9XM3RmRVNoOEY0L1VFSkN2aDd4cC8xNkFTWUs0Y0YrcEwrVVA5cUNYYmkiLCJtYWMiOiJkNDBlOWIyNGVjZDAzZTA5MmRkZWYzMGY1MTUzNzViZDQ2ZDFkODRkNjA3MDQ5MDgyNzM3MzlkMmY3ODQxNDczIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:21:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikd1RHRFMmQ2RVU4MkxEdlEvd1Z5NWc9PSIsInZhbHVlIjoicjdMNjNCNXBvU3IvY0tmTk9WTEZ3UlN4VHUvM0F2cWxNZ0QrQVduT0VoTzcvWitKcU1LMCtUOFBZNyszTVB1MHFweWdHbnNhQUJ2cy80YTYwR1YzQTlocGFTQ1RFWWVSNE4rb3EyN0Z2T015LzBBZGtjYStFWGE2QXFZRjdaK0Q2V0gxQU9zSGlzWDB6V2JKNDBHemtHY08xang1NVFYMzNaWmZ3SDJjZDExOUNsN0JGTUZiWHFNQlBiVGtaRnh1bDlOeVJkb0RhcmZsWkt3QUFyREtpays4ZEdhVGhXZVBaNlVyK21QSXFwRGgzMklYSmNWUndDOUhEZVlUazVld1VYV3dUUnE2MVhMakc5RE1XcUJIZUxKdmYzYkx3eTdTT1J1UVlJOUg1MjNTTDlQcjFxdHRjREZucG9Kb0I3akdsWUc5TE4yVmZwWHNlaFUwTkpVV0RyWWJ4WnJWdGIxdjd3OWwzMGJiM0xVRGtSVnhGVWJuK1FQU2tycGd5M2hzSkhGaEpidmNqdGMvYWNGdFRmT3pObzdVSjlnM20zUmtPRjZVT0NxMkVIYmZWMmx0S3dQbmJKWTdyMVFvbW81S21scHM0Szd5anU3M0pORGdMU25VTVZFRHJpMk5STjFYMlRUbFIza3Z1U2hMWjk0cE02NTFwQm1BTWpjR1lxZG0iLCJtYWMiOiIxOTM5M2M3OTc3MmZlZDYwMTc5ZGMxOTYwOGU4MmU4MTE0NmE2ZWU0YzMxZDIzOTYxNTk4YTA0NGY2MDNhNWE0IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:21:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IksxU0RMWTNncnJ0UzhpdFF1YURWVnc9PSIsInZhbHVlIjoiSUR2eWZCcUd3N3lCZU1lZlBIaFBMYUtOV1NxaFVLQ1ZDNExHSFVsRVlxS0lKWFd3Y29TVC9hTTZwNjZCdUdqOEFHUDFqcXM4NGlVTGMrLy90dURrQzJGZy9RN0hQWVZMVXo0REE2NkljdHpsOGdUZ2tqQzBVQ1pEWXNETnRoNURzYmJPV2lXUHQ4TEJySmViam9IUzVtc1NxRlJrVWdUTDFyVjZrR2U4M2NFdjB5V0doOS9SVnkwbDBOWTc0c2xTdklYZ21aU1lMTk5NWHpsd0d2aXJxMk4waVVUVGJyN21KcUNUQU04VDZwaXVMZ1pobTkxcVNFcjVaZWN3K2lhaEtGSlZTQUQ1OUJlOGRqTXBrWVJGWDdzZG80UTd4Nkp1VEFqcDFmd3RxSjRDQWF6MlRhNjdXYmdVWXlENXEwQzZTeUhFUVFnSVM0b2VUT0dNY3VmQnM5TlFuQXJrK1dxbmtRbm5GVlpNNVFQTFVvaGQvbm1UbFd2K2xIbTIvcXNPbWVzNWVvay9LR2E1azJhb2IvT09qREFZcDhUVzB4dkU0TGhxWGJrV0lHRXJhOGZEUEloWXpVVHRnQ25Idmw3MHZXdUFUTjltbFVmY0hIY0JjQTJGZm9XM3RmRVNoOEY0L1VFSkN2aDd4cC8xNkFTWUs0Y0YrcEwrVVA5cUNYYmkiLCJtYWMiOiJkNDBlOWIyNGVjZDAzZTA5MmRkZWYzMGY1MTUzNzViZDQ2ZDFkODRkNjA3MDQ5MDgyNzM3MzlkMmY3ODQxNDczIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:21:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894153296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-650233395 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-650233395\", {\"maxDepth\":0})</script>\n"}}