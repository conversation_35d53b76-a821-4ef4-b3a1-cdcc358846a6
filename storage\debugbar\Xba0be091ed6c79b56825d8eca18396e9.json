{"__meta": {"id": "Xba0be091ed6c79b56825d8eca18396e9", "datetime": "2025-07-12 16:36:04", "utime": **********.382668, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338163.956391, "end": **********.382682, "duration": 0.42629098892211914, "duration_str": "426ms", "measures": [{"label": "Booting", "start": 1752338163.956391, "relative_start": 0, "end": **********.32824, "relative_end": **********.32824, "duration": 0.37184882164001465, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.328248, "relative_start": 0.3718569278717041, "end": **********.382683, "relative_end": 9.5367431640625e-07, "duration": 0.054435014724731445, "duration_str": "54.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45710200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00252, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.357061, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.016}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.367776, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.016, "width_percent": 13.889}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.37368, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.905, "width_percent": 13.095}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-5971010 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-5971010\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-60354866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-60354866\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-913229317 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913229317\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338161110%7C18%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im0xaDM1OG1vdDRNNmFMSTl5MjEwNXc9PSIsInZhbHVlIjoiZ2FZL3ZWckZvUVppbkZtYVZUanZlUlA3OEFJV0gwQko3cjdRYmlnblY3eEJIdk03Qy9acTRkdk1nZksvMlRmVlZ5aHNpU2JnMTAyYTlMc3cyd3FlNUZIVVdMbVliVmptY3l5MG1DL2NKSXRNNmRFR2pQOTg0TWhybmxvc24zYnlVZFVwSi9YZjRYOFRtRVFJZ2RVQjQxTzRMczBEY1N2UGxSV2N2YSszUDl0NERRYnhvNnBmZEZ0NXhEREpQRlpNRDY2a0w2TE1RWUdnTXZ1cGg2Q0ppY3lpMlJ6a1FCUWk4ZGhCOW13bFNiZldCYlUyd3YzbklTYWpCS0pBa0VSRy9TeThpbE1MSWNQMkErNFIyQlJ4by8zTDlwZk5pS0Y2UTR3U1pydHkwS2pLRGFoM0laOElOZUJyOWhuQ1BCZkF4cGFlbjM3VWpLeUdZQVRmMjJZaUVHWVVFYVJKV2hFdXZYd2p2TlFteXlLMnd5UWVpMVU5UmxyazYzZytrcnpKNENFSVFjN1MyMndvZ0lDYVRrcHVUZWlUR3FIbHd6M01UNUM4anlmQkdtakJiNnk1WGJrVHVQK28ybnd5WUpGbldRTEJkTWVMRGZpR0tyM204K3NhRFkyRnpiUi8vTkNtampUZXR4LzlVVGV2dVFYajFla3JNT1UvdXh4Y2lGbVkiLCJtYWMiOiI0M2Q1ZGZiMzU3NTYzZjhmYWMyNGRhZWYzZGZlM2I3NjM3MDUwMDU1ZjFlMzdlZWM1NWQ3N2YwZTNlMzJjYzFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5jb05GbVk1K2p0RVJ5YnBTWFM3R3c9PSIsInZhbHVlIjoienNTaEtOQnNPWWNXVFR3MUlnaW1ucWhUNnhTZE1RSFpUWlhxUDJ0L2sxU3oxWWdhbzlTTmJDNjNsb1JoMmtENmFWMCtRWjhGODYweGxRSmpJdGpMSnpDTVA3SXMzUXVNcktDaURuSVRZUzdhK3pHZEJDTmF2V0FQa1N6anJNTUo1S0FDckRwVGROWW5obHlXTWcxc3B1em9rMlYrK3BYN01tajZvc21Hbyt5d0kvZXJYMU5DWFVHMWcyaDFDa21UbGcrS1pnblk1dlRDenlJek9OaFBrbDhXUVIyQ0NmUVBVcmFmUHhvWTd4T2tuLy9Sa3haK1VaOEFYd3pmcy80c1dEM3NPa1ZEV1F6cTVjWGlMMmcrT2Zma1VneWVTRUV5L205dzlUSlMvK1lXMlRjeVRiOHZBeXkyeXZpcjUrRHF2alZjUVkxZFV3ZmlnMjFhQWlKRGdrRmRkMFlCcTljUzZHNmFwUXh4Y2gyakJnTXVqZHV5M2p6eEN2RFl2SW9FaE01aTZpZHlzWWtXcnpac1RwVExFQktrSCtoZHpsT0dVcXJpc2tqR0ErWHVjdWlDdzNBTmxUQ25Qa0NvWVhuYnBvQmd2VXdkUnFraTlvV3dLOUdrYzJoTTJ1eHZGMTlJcnBJb25BbHVGTmhzVDhkamZmWldEeCtLR3VaUnNvRkUiLCJtYWMiOiI0YzMyNjUyMzc0NGM1MzA3ZmE4MzY4OTFmN2YyNjBlNTg5NzM4YzYzNjVhMzcwMTJhYzg3ZjdkM2EyZDZiNTEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-459125020 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459125020\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI5ZUZROXkyNmZqUC90aWtkZDg0dkE9PSIsInZhbHVlIjoiN3VucE9OVnNBSXdaVkIyK20wRlZ0U0tTbks5OWdsbGdXbU8zeEEvT28xZXliVGRlL3RMRDZFSHV0S00zRVRrTkNGK05zNm9yS0w4M21NT09SOEFqSEovRHZpUzJYYTVUVHdZT3lYc1NvSHVsb2JkMXl2aWhLTnBQeEttamVDTnlsRVJHa2g5QzRNencvYXhMZlczdHdTdGZtL25OTjFWYTBac1ZGamdBc0IyU0dsd0NSbldYYUVLaFZtNXhabEJXeFFzbXdzM2RobHJ6aGd2bUlLNWExeHpSQVRzSFIyZHpDNVNuckxGMWpsN2NuRTNvN08wOTRmTHY5WkZGVkgrKzMvejZvWEdCNDNpY0FGejZJUUJsTHdqbkVqcDVXd3NodWVoUkpYVG95WXlvNXRZN3UyR25rZ2N5L3h3Yk43SGc2RHpDWG9uREMrcy9qVXAzSTVkdUxoU3hXVEtaNG5LR1I5L2w4aWlmVnZzZDVTOUZTbWRrazBTSTFObXdLVUdXTUgxajZXRm0vYTJ2YXRidDRlN1RDSUZweFU4NFlkTU1VZUdsWXh1TXVVQU9Tb0h6b3FtbzhjclZvZlpDbXhRS1pNRDlhRzdNSjVXd2t2THRra1lQa1lHK1IxS2xUOVdEdXNlL25JZ2dpVnh1emdvakMyR2hQdmNOTkZ0dkdDZnoiLCJtYWMiOiI0Y2FiZTU5NTFmNmY0ODU0YmU5ZjZlYjdmYjg0ODI4YWFiNGEyOWI0NWM3NzM5MjA5YTAxMmUyNDMxMjhkZmUxIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZaY3R3RXp0ZWZkU3dxTHJES05xWkE9PSIsInZhbHVlIjoiYnYzaGx4ckxJSGRiNWdKVVRWd2g1QTg1d2tTVy9pMGNJbVlIc2R6ODhqbStMazlHaHNRRHBmb21oYTZxR3BCcVgzMUdjTU9YbHBteHZBUFJ1VlMvMDM2NmY0cDRFaCtGajYvQS9May8zV3c3a1pBaERnYVpKOXdQVVd3MU44VnFWRjdVays5d1RsTjB4RjZ3UTA3TDY3L0ZWbHpyNDlGZlZKVjJzc09PTklBUFRpcWJjR0lDbkp5RldDeXRkUWNXZnNqanBXQkZuNG5Hd3BwZER4TkVtUUZzcEtMRVNtL0ZsVzZyemM4QVdpcC9sZDgxajBuem9DK0RXVzd5TFc2WXhZd0JSNjZnUzVuZTUrYVhVRG5aZVpiaVByamxkOTFXak5LZUthYU9Dd2IzTlBBSkFEaWNsQ1FUWjJvVEFjNndCRkxmeE1uUjNOcUdEUFYwS1UxaCtleUpHSncvK0JHaVBtdDRzeEdmUGl0ZG44TEtIeVpIekpyYmtja2FHNml3YlBBUWFPb0VkRW5oc2dwMWQ5bkVRbStuSVA5MGFtbkI1LytCQWVVSFc0bnVIdlc4N29NOGdIRWw4SHBnVUNHQm50TFVLc2V4Nm56czdFTzhwQTI4UWJQVGtTTXA3WTVyalFSanBNZWg1TDJWbURRSDd4WWJJbDlSdHpzYm90WC8iLCJtYWMiOiJkZjJkYjA4YzZmMGM3NzVkOGRmNDkyZmYyNDU1Y2VlNWRiOTYzNzk3ZjFmMGMyYzg2NDZkYjAwYTQyNjkzN2FhIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI5ZUZROXkyNmZqUC90aWtkZDg0dkE9PSIsInZhbHVlIjoiN3VucE9OVnNBSXdaVkIyK20wRlZ0U0tTbks5OWdsbGdXbU8zeEEvT28xZXliVGRlL3RMRDZFSHV0S00zRVRrTkNGK05zNm9yS0w4M21NT09SOEFqSEovRHZpUzJYYTVUVHdZT3lYc1NvSHVsb2JkMXl2aWhLTnBQeEttamVDTnlsRVJHa2g5QzRNencvYXhMZlczdHdTdGZtL25OTjFWYTBac1ZGamdBc0IyU0dsd0NSbldYYUVLaFZtNXhabEJXeFFzbXdzM2RobHJ6aGd2bUlLNWExeHpSQVRzSFIyZHpDNVNuckxGMWpsN2NuRTNvN08wOTRmTHY5WkZGVkgrKzMvejZvWEdCNDNpY0FGejZJUUJsTHdqbkVqcDVXd3NodWVoUkpYVG95WXlvNXRZN3UyR25rZ2N5L3h3Yk43SGc2RHpDWG9uREMrcy9qVXAzSTVkdUxoU3hXVEtaNG5LR1I5L2w4aWlmVnZzZDVTOUZTbWRrazBTSTFObXdLVUdXTUgxajZXRm0vYTJ2YXRidDRlN1RDSUZweFU4NFlkTU1VZUdsWXh1TXVVQU9Tb0h6b3FtbzhjclZvZlpDbXhRS1pNRDlhRzdNSjVXd2t2THRra1lQa1lHK1IxS2xUOVdEdXNlL25JZ2dpVnh1emdvakMyR2hQdmNOTkZ0dkdDZnoiLCJtYWMiOiI0Y2FiZTU5NTFmNmY0ODU0YmU5ZjZlYjdmYjg0ODI4YWFiNGEyOWI0NWM3NzM5MjA5YTAxMmUyNDMxMjhkZmUxIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZaY3R3RXp0ZWZkU3dxTHJES05xWkE9PSIsInZhbHVlIjoiYnYzaGx4ckxJSGRiNWdKVVRWd2g1QTg1d2tTVy9pMGNJbVlIc2R6ODhqbStMazlHaHNRRHBmb21oYTZxR3BCcVgzMUdjTU9YbHBteHZBUFJ1VlMvMDM2NmY0cDRFaCtGajYvQS9May8zV3c3a1pBaERnYVpKOXdQVVd3MU44VnFWRjdVays5d1RsTjB4RjZ3UTA3TDY3L0ZWbHpyNDlGZlZKVjJzc09PTklBUFRpcWJjR0lDbkp5RldDeXRkUWNXZnNqanBXQkZuNG5Hd3BwZER4TkVtUUZzcEtMRVNtL0ZsVzZyemM4QVdpcC9sZDgxajBuem9DK0RXVzd5TFc2WXhZd0JSNjZnUzVuZTUrYVhVRG5aZVpiaVByamxkOTFXak5LZUthYU9Dd2IzTlBBSkFEaWNsQ1FUWjJvVEFjNndCRkxmeE1uUjNOcUdEUFYwS1UxaCtleUpHSncvK0JHaVBtdDRzeEdmUGl0ZG44TEtIeVpIekpyYmtja2FHNml3YlBBUWFPb0VkRW5oc2dwMWQ5bkVRbStuSVA5MGFtbkI1LytCQWVVSFc0bnVIdlc4N29NOGdIRWw4SHBnVUNHQm50TFVLc2V4Nm56czdFTzhwQTI4UWJQVGtTTXA3WTVyalFSanBNZWg1TDJWbURRSDd4WWJJbDlSdHpzYm90WC8iLCJtYWMiOiJkZjJkYjA4YzZmMGM3NzVkOGRmNDkyZmYyNDU1Y2VlNWRiOTYzNzk3ZjFmMGMyYzg2NDZkYjAwYTQyNjkzN2FhIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}