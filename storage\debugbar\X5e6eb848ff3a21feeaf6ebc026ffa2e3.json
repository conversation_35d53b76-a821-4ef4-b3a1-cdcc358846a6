{"__meta": {"id": "X5e6eb848ff3a21feeaf6ebc026ffa2e3", "datetime": "2025-07-12 16:35:38", "utime": **********.295301, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338137.856971, "end": **********.295318, "duration": 0.43834686279296875, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1752338137.856971, "relative_start": 0, "end": **********.226606, "relative_end": **********.226606, "duration": 0.36963486671447754, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.226617, "relative_start": 0.3696460723876953, "end": **********.29532, "relative_end": 2.1457672119140625e-06, "duration": 0.06870293617248535, "duration_str": "68.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43874584, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02177, "accumulated_duration_str": "21.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.260556, "duration": 0.02095, "duration_str": "20.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.233}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2852101, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 96.233, "width_percent": 3.767}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2422 => array:9 [\n    \"name\" => \"عصير المراعي مانجو فواكه مشكلة 300 مل\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"id\" => \"2422\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2421 => array:8 [\n    \"name\" => \"ماجي دجاج قليل الملح 18 جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"id\" => \"2421\"\n    \"originalquantity\" => 6\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1136947977 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136947977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1707463207 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1707463207\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1826423564 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338115835%7C17%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iit2eVRCM0FOUzV1enQrYW9JRS95Z1E9PSIsInZhbHVlIjoiWWJaVlM4YWo0eWQ4ZW9sNDdUdW56a2tmd2VENk0xbmVpYjFPWE5adU5wM3YvSFF5ck52cmJkbXlxbWJxWFdJMFJWcE8rNlY1OUlYMkRkejV2SGxZN3owSUwwK0NGL2puZWI3M3pGQ3ZVdFM3TUdkL24xK0ZXRkpmMG5PYjNCVFJ3cmhXeDBsbkpEUmhjNGZsZm5odG5mWml5bHFhK3c0cytZelM1c2ZKMFZvS0hxWk4rQkFqeUFKL2d6cDEvNU14OUUybTFoTHhvUnp1dmppQ1h2QW1rRVFudFRsQ0gzRmYwRkdGbG5peXJoeE5SeG92ckhnOGEybzhGTWh3a0xNVGFheVpPODBwTkNhcHBObFNwdjVub3ZFTGhPL1JTWng2c0V4VVBEaUtFZE1ZM2RHeEZmMnJlb2tSY1JZMXdneGdZcTJmQ1FwVFFLMUcrOVgyNEgyK3c5YVZaK1BaTmtscjg3QUQ3SE5nTWRBRUM2bUxPQUllY0FOMENHTTZSTUJ6cnpEZ29LTEprdnpIdVlhTGZxeTZvdlFZZkdFTXhKblhndTVPaTFKUjhNcTY3d240TElYZjZBZ0EybVNmY3REekdaSlZSc3R1NWdraGZ1WDRtdG4rVGlQUFlUN2dyMkw0RFliQ1p0ZGNXTy8rT01FOEpTN1NMRzNITnh6MVlMaXEiLCJtYWMiOiI3NmRiYThjYmVhMWQyMmVjZTFjMGUxMmE4MmIwYmQzNGQwYmEyMTRmODIyZDI2ZDUxMTA2NWJlZGIxZTcwOGM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5FTWFYa2xYTndJOXdqS0l5YU5IWmc9PSIsInZhbHVlIjoidUoxWmgvbUVGZHNhWi9sWGFQYXdEOGNYallPQURYV1lrNm1ZQ1AvN21KRVVhbWMxZDZYalF3QXVKYkk3SGlTazR2eTZiMFd5M0s3V0VnckZEajQveTFWeTA4T1BSNzNHNEpUMFZIcjFjbHBOekUzSStyWTR0U2kvWjdUVUhwZVl5aFl6Umt0bjRMTVJYUlk1NXdUYXQ5WVVyY0RhQnk0L2puOHhUMlU5Nlgvdk5sNWI2OGpDNU54aDkwYk04dE0vRFFMcHNUYWxJdzB4SDlObGxEbnp3RnpMRWlNSi8xRWtqQkdnUEN4cjBtWlZJdVdheDJsZjJscVo5RExYUjh6VHp4MVpiSjZvK0sxWm1MVnVFOVdtYkVjb2gwT0FzQmVhUjZmMVdKNE15TndiYnFtUXpOVDdzY1ZXVUxid2ZDRGVjVlhZUWlPSS9lWXczZjBYQTcvWWJqTzVRckx1R0E2czdlMEhEYUR4eHVBeE4zM2gyN3RqUUdjcVVaVURUZFhqdnNrUHJLQUwxU1IrVDVibFh6OXRrRU1OdjB3RFB0V0l6bUhtc1FtVVFvNHVEQjVVaDl1RkNQZlczczdIVEpYTnZpVUlMNjkxTXBia2FnT1pFOTlXUkF4M2JqMkVQWlJ5ejdRb1d2RnplR3VLZ0VTWUJ1Z2VGU1o5WFozd1ozQ0siLCJtYWMiOiJhN2UyNzY2NzUwNTU1N2JlNWQ2ZGQzN2FlMzc0OWQ4YjkyMjQyN2I1Y2IzZTBjNGJjNzdhNjVhNjMxMTUyNjlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826423564\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2095459603 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095459603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1413971140 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:35:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlvNkpFNUUwNTBlU05jWGxJS09mOFE9PSIsInZhbHVlIjoiWjQrQUU5ZG1GM3lPZmwxblIzS2RCOUxSOTcvditGbEFCZ01xVlVWL1c5SlJtcVE4NVdoc0RGVGgyZmpBanBFV0Nnb2JidyswMmNBK1FPSzhyUEhWdkNmVWdZTWlQUkoraUVSdE1aTzA2QkFuSzJXenVpWFdmMnMxbUpYdk5IYUZkQm5CZ1dhaEJoQldNcnJjZjNyK1ZRc1lCVnduVWdvRWdPSUI3ZVgwWXRNdEpPdzZqSUtoOVkzc3Z3OUtXdk9mSnNtTkF3Rm5vZnh4YVJ5TVhxejdLSWlyYVl3Wm9TWHdrUTJPTy9DSHpNNHhMMWcrSGsva0lxSCtJcE43NzdwZHM4L1BiMlkwTWRuemw3S0h0aVdDdStRd1doTFV5MW9uNjMvLzl5VkhiUnB5cGdVelY4WXRzczlYS0s2MXVwUEI2T3pGVU5KazZMWlF5UWQ1dmg5ekEwVS9lQXB2K1daYmxWampBN044RWdLZ09tMWNmMDhZL3VSeU5lMjIyZmx6bkhRVnZmRDEvODNmNDF5TXhYU0k2d1o2OEtoRE1qeEVoZlRJbUpQb04wWWUxM3U3emI3RlhFQ3UwTGJHdmhoNHpxdmhNd2sxOTJUTEd3RUtkTnhqdWsxUzlxb1owU3hYZ0tpYm9TckNKaDQ3TzU5a0dXK0NrM1VzMmpRR1B3bGsiLCJtYWMiOiI0YTMyMThmOGUxYjFjODc0ODJlZjRkM2U5YTc1ZTY1ZmMyMTYxYTcxZDQ5N2I0ZWYxY2M2NGM0YmM1OWU5NTJkIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImN1amJqNTExMG83SHFOVVZQNlJsOFE9PSIsInZhbHVlIjoieEZCb3ZJb0xlR1pwUXFBN29ya1FxeFJaR0Zuem5TOGZ1WndSOU5sV3d2QjVBbS90MHdSRm5DejZqQ3hMWXBQVXJ4Q3ZmOWtuYWwraXFQd0I0YUNTWCs3aW82UmRVMUdlZGtKcjRuTjBYL0k3cVFORGNoUDF5U1ZOb254dThTakNkZnBOekFkVWcwTkdXRVhKRTlscnRraERvWXZrTFk0UGd1d2pFdDB2cFppYmYwRnY4Q1J3R21MSGtnUElORC9wMklBQjRDeEw0QTMxUENOaU9HZnBBeko1bUdRMWVHLzhMMzZCOENYcmhKTXFqUUNrSkxSNHJNbHZ6MmZ0M2NnQXkxQkpGWXNWd3d6U0JFU05pYmUzY1RlN0hSbXFCNmdVQUNSQUk0ODUyUUFXRSt2MitKQ1NBQXB4Y3YvcVhDWkRtM2RPODcrSHQ3d29zdEJzTXJpZUd6dUtJTjRGR2tCalRUbFVnTFgvZU0ydTZWcjJ2cWxKR0VyMUpORFAzY0tuZUhxbmFiUDU0VXYxRTFwOU81ZmN4aGZYUDNMTENmeWM4S25Ba0tRMlNBblRnSy8yZUw1M2F6OWdHcU1EMFJuUWF6SHZCMUxYbk9qZ1RRM003WGNLazljVlJMUFdLSXBsQzNoM2lJdVdYOWFqSGRPeE5xY00wMFRvTHRvM002VC8iLCJtYWMiOiJiZDZjYmVlMWMyMDhkZWFiMmNlZmI5YTliOGMwNjI0ZjdjOGYwOTNlNTA2MGE3NWE2ZjJjZTBlMzBlNjE1NDBiIiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:35:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlvNkpFNUUwNTBlU05jWGxJS09mOFE9PSIsInZhbHVlIjoiWjQrQUU5ZG1GM3lPZmwxblIzS2RCOUxSOTcvditGbEFCZ01xVlVWL1c5SlJtcVE4NVdoc0RGVGgyZmpBanBFV0Nnb2JidyswMmNBK1FPSzhyUEhWdkNmVWdZTWlQUkoraUVSdE1aTzA2QkFuSzJXenVpWFdmMnMxbUpYdk5IYUZkQm5CZ1dhaEJoQldNcnJjZjNyK1ZRc1lCVnduVWdvRWdPSUI3ZVgwWXRNdEpPdzZqSUtoOVkzc3Z3OUtXdk9mSnNtTkF3Rm5vZnh4YVJ5TVhxejdLSWlyYVl3Wm9TWHdrUTJPTy9DSHpNNHhMMWcrSGsva0lxSCtJcE43NzdwZHM4L1BiMlkwTWRuemw3S0h0aVdDdStRd1doTFV5MW9uNjMvLzl5VkhiUnB5cGdVelY4WXRzczlYS0s2MXVwUEI2T3pGVU5KazZMWlF5UWQ1dmg5ekEwVS9lQXB2K1daYmxWampBN044RWdLZ09tMWNmMDhZL3VSeU5lMjIyZmx6bkhRVnZmRDEvODNmNDF5TXhYU0k2d1o2OEtoRE1qeEVoZlRJbUpQb04wWWUxM3U3emI3RlhFQ3UwTGJHdmhoNHpxdmhNd2sxOTJUTEd3RUtkTnhqdWsxUzlxb1owU3hYZ0tpYm9TckNKaDQ3TzU5a0dXK0NrM1VzMmpRR1B3bGsiLCJtYWMiOiI0YTMyMThmOGUxYjFjODc0ODJlZjRkM2U5YTc1ZTY1ZmMyMTYxYTcxZDQ5N2I0ZWYxY2M2NGM0YmM1OWU5NTJkIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImN1amJqNTExMG83SHFOVVZQNlJsOFE9PSIsInZhbHVlIjoieEZCb3ZJb0xlR1pwUXFBN29ya1FxeFJaR0Zuem5TOGZ1WndSOU5sV3d2QjVBbS90MHdSRm5DejZqQ3hMWXBQVXJ4Q3ZmOWtuYWwraXFQd0I0YUNTWCs3aW82UmRVMUdlZGtKcjRuTjBYL0k3cVFORGNoUDF5U1ZOb254dThTakNkZnBOekFkVWcwTkdXRVhKRTlscnRraERvWXZrTFk0UGd1d2pFdDB2cFppYmYwRnY4Q1J3R21MSGtnUElORC9wMklBQjRDeEw0QTMxUENOaU9HZnBBeko1bUdRMWVHLzhMMzZCOENYcmhKTXFqUUNrSkxSNHJNbHZ6MmZ0M2NnQXkxQkpGWXNWd3d6U0JFU05pYmUzY1RlN0hSbXFCNmdVQUNSQUk0ODUyUUFXRSt2MitKQ1NBQXB4Y3YvcVhDWkRtM2RPODcrSHQ3d29zdEJzTXJpZUd6dUtJTjRGR2tCalRUbFVnTFgvZU0ydTZWcjJ2cWxKR0VyMUpORFAzY0tuZUhxbmFiUDU0VXYxRTFwOU81ZmN4aGZYUDNMTENmeWM4S25Ba0tRMlNBblRnSy8yZUw1M2F6OWdHcU1EMFJuUWF6SHZCMUxYbk9qZ1RRM003WGNLazljVlJMUFdLSXBsQzNoM2lJdVdYOWFqSGRPeE5xY00wMFRvTHRvM002VC8iLCJtYWMiOiJiZDZjYmVlMWMyMDhkZWFiMmNlZmI5YTliOGMwNjI0ZjdjOGYwOTNlNTA2MGE3NWE2ZjJjZTBlMzBlNjE1NDBiIiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:35:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413971140\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2422</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"37 characters\">&#1593;&#1589;&#1610;&#1585; &#1575;&#1604;&#1605;&#1585;&#1575;&#1593;&#1610; &#1605;&#1575;&#1606;&#1580;&#1608; &#1601;&#1608;&#1575;&#1603;&#1607; &#1605;&#1588;&#1603;&#1604;&#1577; 300 &#1605;&#1604;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2422</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2421</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1575;&#1580;&#1610; &#1583;&#1580;&#1575;&#1580; &#1602;&#1604;&#1610;&#1604; &#1575;&#1604;&#1605;&#1604;&#1581; 18 &#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2421</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}