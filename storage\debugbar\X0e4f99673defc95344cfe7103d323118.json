{"__meta": {"id": "X0e4f99673defc95344cfe7103d323118", "datetime": "2025-07-12 16:36:17", "utime": **********.455665, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752338176.96436, "end": **********.455688, "duration": 0.49132800102233887, "duration_str": "491ms", "measures": [{"label": "Booting", "start": 1752338176.96436, "relative_start": 0, "end": **********.388034, "relative_end": **********.388034, "duration": 0.4236741065979004, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.388044, "relative_start": 0.42368412017822266, "end": **********.455691, "relative_end": 3.0994415283203125e-06, "duration": 0.06764698028564453, "duration_str": "67.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45708616, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.422163, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.588}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.43442, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.588, "width_percent": 17.32}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.440547, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.908, "width_percent": 12.092}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Im50SE5ibDlmK0hlb1J2dGVsV1JvSkE9PSIsInZhbHVlIjoibCtELzdnblJqVUgxQnFKTzdlQWE0QT09IiwibWFjIjoiZDlmYTA1NGEyODZlM2YyZGQ1NTEyOWFkOTQ0MzRiYmM1NzU1M2MxZDY4MDI2YmJhMTQxNWVmNTQ2Njg3M2VjYyIsInRhZyI6IiJ9\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1337149916 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1337149916\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-110388409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-110388409\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-960119841 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960119841\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-645188511 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Im50SE5ibDlmK0hlb1J2dGVsV1JvSkE9PSIsInZhbHVlIjoibCtELzdnblJqVUgxQnFKTzdlQWE0QT09IiwibWFjIjoiZDlmYTA1NGEyODZlM2YyZGQ1NTEyOWFkOTQ0MzRiYmM1NzU1M2MxZDY4MDI2YmJhMTQxNWVmNTQ2Njg3M2VjYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=10dm9x5%7C2%7Cfxj%7C0%7C2019; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1eejy7q%7C1752338173245%7C20%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdRTlBKL0NCVGJNRHcwajV5YnM2THc9PSIsInZhbHVlIjoiaG1MRWRHcTdhUk5QUXR4WjQvNDV5MkF1WVNUbUJ4YStPTDRLb3RXc0ZHMk9wbk9oeENhVVRvcEpuNmVISXBPOU8zQmhEc2FzMllFaHcrcG5LSU56VkRLRFVPTm8zQ2ROb1NFNkUrRkd0dGFlUHppS1lwTzRJM0pEYzFRVllmWFBVYXR0Q1JPcFpuak1lZHZSYnZSWFhPWGgxdmpURnZvZmt3TjAwV3ZmZXA1VUwrcXkzeHRaekdCMlQ5ZkpIanhxL216QkE1c0R6akFJQTlWSU1vWFFURlZadGw3b3F3MHd5d0g5YVpjL3o0UHY3VlZLeHdlNi9oQVdKZEQ1R3d1SW8rckEvQVYxdUVONnVjU2tkYkhzTFBTSzllODlQU0h2K0lEc3R4dURBTTRsT2FkUmtvSG5nUno4RFZHRGVqdUUrT2l6aUlXb1BmTVRpTGdGTFJ2VTVHM01RcXhhS1RxZk9BK0VTWkZMTGo5bXZQeXVYQTVkVXhIcmtZbS9qbmo0aE1GQklhRk5hM28xRVJuZ0dLNkNYMVdFSmszNnk4V0FJL2dUWnhFQnJMakhDajBKZWdUMmwxa2cwWi9aN1kyaS9ZTGl0S3RnZDRodkU1R3JOT3FSK2xzRUJwaFRVUEZlUGs3SllUNFEwQ0ZyK09FTUdlbUxXTFZXVU5oWVQzM0MiLCJtYWMiOiJkZTdjYzQ4ZDgxYTYwYzU2OWU0YTM3YjlmMWY3YjMyOTMwM2ZmMjQzZmJmNzhjMjkyZjYxOTE4MWRiZTNiMzg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkMyb2d3aWRpblBVSVluSEE5WXcrUkE9PSIsInZhbHVlIjoiU0Vkcm9EVGFRVzhKQysxd2RsWkNSZ3hkdzlKVHl5SGhJRmtOcEtMOEZ4eTB0dDJOTDI3QnZjdDIrOXdrTFkzNlllSFdNaFNkM2RNMXIreXBaQitzWCtENFZ1VzR0Qi9aUUJOcFVKNkJSMGMwZUtpY0Fldjk3aEFBQVdWc2dvdkwwUC9aQkNwd0Q5SkE4UnZJQng4SGJIZWFaYUZqY0tEL0lVVGlIUUZya1ZtM3NNRk9yeXdIS3RqeEM1ZXluamNjWkJwK2cxQ1ZuUUVFdzkwVkRCZnlmNFpZaHJQa2RadDFETkMwV2JRbFpzRGJRbTgzUVNqZk1aeG1vYU94YldvOW1FcUhSakpBNVhiY24rRm9tQ0o0ZXQ4QU5qMXd4Rk42aXFMTiszL2Y0SnZGcEdkL1NZVjVWRVREZjdnV0szWGxDNXBIN3pOR3l0UXBZOExhdldJcmczQWJuS3VUbkd5c0xkR0dSSmJyaVJYbGdDdDdVdVRMRjA0dndFdjBWRnRHdmZRVERJUmlZcW5tUDJPb0FVb1BFd05WTlRXVmg0azJPc3hxeW9mRlF2dXdvRE5wQlIvZXRiK1NZV0taenNNOHFaUUxmVmpvbGJ1S1o2RXNWSnlNZTNHNXRQYU16aG02MzFSQnJNeFBWR0JwNDBIU2tXd09GUTliWjBpajIrNisiLCJtYWMiOiJkMDM2N2QwZGEzMjRjYTU2ZWJkNDk0NGE2NjIyYTBjYjEyMzZjYzU2Mzg1NWIzZjMxM2Y0NjY3NzJlYmY3NTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645188511\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-841369769 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JZlW1dO7X8l2hBvrTrSUsULH3FhSE5rQEHtmkkEz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841369769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1205123041 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 16:36:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJLUEx5bURUOHoxVWJoRE1aVFFxeVE9PSIsInZhbHVlIjoiNHY0WldkSEdyZ2g2aEVJbWp6MndUakNIWURVOGRDTWI3RXFmejJtRmRLamU3TEhJN2JjYjQ3V3V5ZE9vNU45WTdhNzl0YnJGUHhoOVQrbXYvME5FbmxkQUd6M0NQcmd3Y0ZMaVZmT1dkK0RBakgzVDBvazJaVFExWHcvWDRuc0UrdkRONUh4RUxSSDFuTmw3RFZkUnNMNEYzUXdFZ0VCbUtwaGN6MEVwMFJ1WmJsVEpHR1NYVjJYUkZzcHRsVVF3dE0zL29kWDRGUXAwNDR5ajQ5YjRac0EzVHVVNEZXT1IxamZzTWNITk1kVDN5QTVyNXVLOGZEL2FsMW1XYThkaUtUNVYwY0dsNURhRjhXTzhSUGxMOUtpSk4zU2VGQmJaZEsxVFluRU9YNjB6VitKaUIyaVBuTWNrbHA5bUZoS0tXSVRQWi9VTHBwbFF3OENoSk5JYkVUbVNKWEx6OXpLcWcwNXpLdERHNlpENzdpN1R1dDJ0TmNUa0RNaFBYSk1keXJpNjlydksyZHdxaHovYkxoUGdCVmdTVUZTOW1wL25MZ3FtWHVvR1dPak5GaUdyVVNJNlIvSmwrM0srakhsbHpmYmlHT1FMYUdvWHBlYUp6SGJQUHVPZU0yTTV2eWE0d0VFenB3Nk1ZMno5cGs5YktYYUtPamxxbEJ2U2pTV1oiLCJtYWMiOiJhMjJkYmQwMDBiYzNlMjJhMTgxM2ExZmNmNDI2Y2U3MzZjOGI1MWY3NzBhNTNkYzM0ZDZlNTgwNTNjYWJlYzI3IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlzNFM4VEw1aWxUU3N1cjVtdlhKK2c9PSIsInZhbHVlIjoia0lKNEIvV0lMSWZvMm4vck5oTWhUREJMcTBocVEvK2FVY1RQTWNZcE1MUWZjRjV3TDg1VkNWL0pTU3kxS2RNWUdPd0pOQnNTeSszVXFVeWptYzUxNTVOQ2VWMWMrQlIvbHYrTWxLNDlhSDN3Z2l0MnBxdlNCdXRHZDk4RDJ6UElKMFpqYmN6SWJ6SkJ1K0xQWGoveENZc1FmcXVTaFRvbWpUWmlMUHVWNUF0WmxzeDNIdUNXTnMyVTdiNGw2OUZHMTdIb0owdlRMTVgzVklHN29zVDRKa0hKNHB5RlNCTnEyejR4S1hiVUY3eWM5a3BFY2JQMGU5V3JnMG56c2gwMEwvUUIvckJXRXZjc3hnV1E3S3pxR1Z0SnA0bmlZUEZZcCtISllUZWZLZmI3M25WQWFSUUlCNVVvMTErWU1oK0NCd3hZQmFldXJpWENERmlKemhFU0Z1MTlCREMwaE1iSVl3Y1Z0OW9NUFM2VDEvVy9LWXJmV1dUdm9jd24vK1lDWXBadnFQUFZUZkxnSHY4OFNYVFlIbzRoRlBwYUtuc2xScHE2OHZaaU5IemEwOE9mYnFDUmJ0alFjMHoyUFhySGJWOWdPNVpEZmVvVy9vTk1mT2Q3ZUwwS0k1dUtSR3R3YmJJS3Y3YlNYTjdpTjFpT0ZQN0FXbVJYTXhneFdjMVciLCJtYWMiOiJmNzc5Y2JhNjAzNzMzMDJlN2Y4ZWMxYzUwYjBkYzQxM2JmZmM5YzA3MTE0YjJiNzAzOTAwMjRlNGIyNGQ0MjE3IiwidGFnIjoiIn0%3D; expires=Sat, 12 Jul 2025 18:36:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJLUEx5bURUOHoxVWJoRE1aVFFxeVE9PSIsInZhbHVlIjoiNHY0WldkSEdyZ2g2aEVJbWp6MndUakNIWURVOGRDTWI3RXFmejJtRmRLamU3TEhJN2JjYjQ3V3V5ZE9vNU45WTdhNzl0YnJGUHhoOVQrbXYvME5FbmxkQUd6M0NQcmd3Y0ZMaVZmT1dkK0RBakgzVDBvazJaVFExWHcvWDRuc0UrdkRONUh4RUxSSDFuTmw3RFZkUnNMNEYzUXdFZ0VCbUtwaGN6MEVwMFJ1WmJsVEpHR1NYVjJYUkZzcHRsVVF3dE0zL29kWDRGUXAwNDR5ajQ5YjRac0EzVHVVNEZXT1IxamZzTWNITk1kVDN5QTVyNXVLOGZEL2FsMW1XYThkaUtUNVYwY0dsNURhRjhXTzhSUGxMOUtpSk4zU2VGQmJaZEsxVFluRU9YNjB6VitKaUIyaVBuTWNrbHA5bUZoS0tXSVRQWi9VTHBwbFF3OENoSk5JYkVUbVNKWEx6OXpLcWcwNXpLdERHNlpENzdpN1R1dDJ0TmNUa0RNaFBYSk1keXJpNjlydksyZHdxaHovYkxoUGdCVmdTVUZTOW1wL25MZ3FtWHVvR1dPak5GaUdyVVNJNlIvSmwrM0srakhsbHpmYmlHT1FMYUdvWHBlYUp6SGJQUHVPZU0yTTV2eWE0d0VFenB3Nk1ZMno5cGs5YktYYUtPamxxbEJ2U2pTV1oiLCJtYWMiOiJhMjJkYmQwMDBiYzNlMjJhMTgxM2ExZmNmNDI2Y2U3MzZjOGI1MWY3NzBhNTNkYzM0ZDZlNTgwNTNjYWJlYzI3IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlzNFM4VEw1aWxUU3N1cjVtdlhKK2c9PSIsInZhbHVlIjoia0lKNEIvV0lMSWZvMm4vck5oTWhUREJMcTBocVEvK2FVY1RQTWNZcE1MUWZjRjV3TDg1VkNWL0pTU3kxS2RNWUdPd0pOQnNTeSszVXFVeWptYzUxNTVOQ2VWMWMrQlIvbHYrTWxLNDlhSDN3Z2l0MnBxdlNCdXRHZDk4RDJ6UElKMFpqYmN6SWJ6SkJ1K0xQWGoveENZc1FmcXVTaFRvbWpUWmlMUHVWNUF0WmxzeDNIdUNXTnMyVTdiNGw2OUZHMTdIb0owdlRMTVgzVklHN29zVDRKa0hKNHB5RlNCTnEyejR4S1hiVUY3eWM5a3BFY2JQMGU5V3JnMG56c2gwMEwvUUIvckJXRXZjc3hnV1E3S3pxR1Z0SnA0bmlZUEZZcCtISllUZWZLZmI3M25WQWFSUUlCNVVvMTErWU1oK0NCd3hZQmFldXJpWENERmlKemhFU0Z1MTlCREMwaE1iSVl3Y1Z0OW9NUFM2VDEvVy9LWXJmV1dUdm9jd24vK1lDWXBadnFQUFZUZkxnSHY4OFNYVFlIbzRoRlBwYUtuc2xScHE2OHZaaU5IemEwOE9mYnFDUmJ0alFjMHoyUFhySGJWOWdPNVpEZmVvVy9vTk1mT2Q3ZUwwS0k1dUtSR3R3YmJJS3Y3YlNYTjdpTjFpT0ZQN0FXbVJYTXhneFdjMVciLCJtYWMiOiJmNzc5Y2JhNjAzNzMzMDJlN2Y4ZWMxYzUwYjBkYzQxM2JmZmM5YzA3MTE0YjJiNzAzOTAwMjRlNGIyNGQ0MjE3IiwidGFnIjoiIn0%3D; expires=Sat, 12-Jul-2025 18:36:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205123041\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1436984975 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKTaTSHh6wnH6tQvVOsdxIwMcis3k2jBc8HLRxx2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Im50SE5ibDlmK0hlb1J2dGVsV1JvSkE9PSIsInZhbHVlIjoibCtELzdnblJqVUgxQnFKTzdlQWE0QT09IiwibWFjIjoiZDlmYTA1NGEyODZlM2YyZGQ1NTEyOWFkOTQ0MzRiYmM1NzU1M2MxZDY4MDI2YmJhMTQxNWVmNTQ2Njg3M2VjYyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436984975\", {\"maxDepth\":0})</script>\n"}}